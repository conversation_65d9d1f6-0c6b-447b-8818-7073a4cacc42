#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
from pymongo import MongoClient
from datetime import datetime
import re
import sys
import os
import gzip
import glob
try:
    # 尝试相对导入（当作为模块导入时）
    from .enums import Permissions, Status, Source, DeleteStatus
except ImportError:
    # 直接导入（当直接运行脚本时）
    from enums import Permissions, Status, Source, DeleteStatus

def find_xml_file(module_type):
    """
    从 data 目录获取指定模块的XML文件

    Args:
        module_type: 模块类型 ('artists', 'labels', 'masters', 'releases')

    Returns:
        找到的文件路径，如果没找到返回None
    """
    # 尝试多个可能的数据目录路径
    possible_dirs = ['data', 'deploy/windows/data', '../deploy/windows/data']

    for data_dir in possible_dirs:
        if os.path.exists(data_dir):
            pattern = os.path.join(data_dir, f'*_{module_type}.xml.gz')
            found_files = glob.glob(pattern)
            if found_files:
                break
    else:
        found_files = []

    if not found_files:
        print(f"❌ 未找到 {module_type} 模块的XML文件")
        print(f"   搜索路径: {possible_dirs}")
        return None

    # 如果找到多个文件，选择最新的（按文件名排序）
    if len(found_files) > 1:
        found_files.sort()
        selected_file = found_files[-1]
        print(f"🔍 找到多个文件，选择最新的: {selected_file}")
    else:
        selected_file = found_files[0]
        print(f"✅ 检测到文件: {selected_file}")

    return selected_file

# 配置参数
XML_FILE = find_xml_file('artists')

if not XML_FILE:
    print("❌ 错误: 无法找到 artists 模块的XML数据文件")
    print("请确保文件存在于当前目录或 data 目录下，文件名格式: discogs_YYYYMMDD_artists.xml.gz")
    sys.exit(1)

MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')
MAX_RECORDS = int(os.getenv('MAX_RECORDS', '0'))  # 最大处理记录数，设置为0表示处理全部数据

# 输出文件路径
OUTPUT_FILE = os.getenv('OUTPUT_FILE', 'process_output.txt')

# 错误追踪文件路径
ERROR_FILE = os.getenv('ERROR_FILE', 'artists_parsing_errors.txt')

# 进度文件路径
PROGRESS_FILE = os.getenv('PROGRESS_FILE', 'progress/artists_progress.json')

# 断点续传配置
CHECKPOINT_INTERVAL = int(os.getenv('CHECKPOINT_INTERVAL', '1000'))  # 每处理多少条记录保存一次进度

def load_progress():
    """从进度文件加载上次的处理位置"""
    import json

    if not os.path.exists(PROGRESS_FILE):
        return 0  # 如果没有进度文件，从头开始

    try:
        with open(PROGRESS_FILE, 'r', encoding='utf-8') as f:
            progress_data = json.load(f)
            resume_from = progress_data.get('resume_from_record', 0)
            print(f"📖 从进度文件读取：上次处理到第 {resume_from} 条记录")
            return resume_from
    except Exception as e:
        print(f"⚠️ 读取进度文件失败: {e}，从头开始处理")
        return 0

# 动态加载断点续传位置
RESUME_FROM_RECORD = load_progress()

# 确保输出文件不存在
if os.path.exists(OUTPUT_FILE):
    os.remove(OUTPUT_FILE)

# 确保错误文件不存在
if os.path.exists(ERROR_FILE):
    os.remove(ERROR_FILE)

def write_output(message, print_to_console=True):
    """将消息写入输出文件，并可选择是否打印到控制台"""
    with open(OUTPUT_FILE, 'a', encoding='utf-8') as f:
        f.write(message + '\n')

    if print_to_console:
        print(message)

def write_error(message, xml_content="", line_number=0):
    """将错误信息写入错误追踪文件"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    with open(ERROR_FILE, 'a', encoding='utf-8') as f:
        f.write(f"[{timestamp}] Line {line_number}: {message}\n")
        if xml_content:
            f.write(f"XML Content Preview: {xml_content[:200]}...\n")
        f.write("-" * 80 + "\n")

def save_progress(processed_count, total_records_found):
    """保存处理进度到文件"""
    import json
    import os

    # 确保progress目录存在
    progress_dir = os.path.dirname(PROGRESS_FILE)
    if progress_dir and not os.path.exists(progress_dir):
        os.makedirs(progress_dir)

    progress_data = {
        'processed_count': processed_count,
        'total_processed': RESUME_FROM_RECORD + processed_count,
        'total_records_found': total_records_found,
        'last_update': datetime.now().isoformat(),
        'resume_from_record': RESUME_FROM_RECORD + processed_count
    }

    try:
        with open(PROGRESS_FILE, 'w', encoding='utf-8') as f:
            json.dump(progress_data, f, indent=2, ensure_ascii=False)
    except Exception as e:
        write_output(f"保存进度失败: {e}", False)

def connect_to_mongodb():
    """连接到MongoDB并返回数据库对象"""
    try:
        client = MongoClient(MONGO_URI)
        # 测试连接
        client.admin.command('ping')
        return client, client[DB_NAME]
    except Exception as e:
        write_output(f"❌ MongoDB连接失败: {e}")
        raise

def verify_database_operation(operation_result, operation_type, record_id):
    """验证数据库操作是否成功"""
    try:
        if operation_type == "update_one":
            if operation_result.acknowledged:
                if operation_result.upserted_id or operation_result.modified_count > 0:
                    return True
                else:
                    write_output(f"⚠️ 数据库操作无效果 - ID: {record_id}", False)
                    return False
            else:
                write_output(f"❌ 数据库操作未确认 - ID: {record_id}", False)
                return False
        elif operation_type == "insert_one":
            if operation_result.acknowledged and operation_result.inserted_id:
                return True
            else:
                write_output(f"❌ 数据插入失败 - ID: {record_id}", False)
                return False
        return False
    except Exception as e:
        write_output(f"❌ 验证数据库操作时出错 - ID: {record_id}, 错误: {e}", False)
        return False



def safe_database_operation(collection, operation_type, *args, **kwargs):
    """安全的数据库操作，包含重试机制"""
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        try:
            if operation_type == "update_one":
                result = collection.update_one(*args, **kwargs)
            elif operation_type == "insert_one":
                result = collection.insert_one(*args, **kwargs)
            else:
                raise ValueError(f"不支持的操作类型: {operation_type}")

            return result
        except Exception as e:
            retry_count += 1
            if retry_count >= max_retries:
                write_output(f"❌ 数据库操作失败，已重试{max_retries}次: {e}", False)
                raise
            else:
                write_output(f"⚠️ 数据库操作失败，正在重试({retry_count}/{max_retries}): {e}", False)
                time.sleep(1)  # 等待1秒后重试

def safe_insert_operation(collection, document):
    """安全的插入操作，包含重试机制"""
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        try:
            result = collection.insert_one(document)
            return result
        except Exception as e:
            retry_count += 1
            if retry_count >= max_retries:
                # 如果是重复键错误，记录但不抛出异常
                if "duplicate key" in str(e).lower() or "11000" in str(e):
                    write_output(f"⚠️ 重复ID跳过 - ID: {document.get('id', 'Unknown')}", False)
                    return None  # 返回None表示跳过
                else:
                    write_output(f"❌ 数据库插入失败，已重试{max_retries}次: {e}", False)
                    raise
            else:
                write_output(f"⚠️ 数据库插入失败，正在重试({retry_count}/{max_retries}): {e}", False)
                time.sleep(1)  # 等待1秒后重试

def extract_field(content, field_name):
    """从XML内容中提取指定字段的值"""
    pattern = f'<{field_name}>(.*?)</{field_name}>'
    match = re.search(pattern, content, re.DOTALL)
    return match.group(1) if match else None


def extract_namevariations(content):
    """提取namevariations字段"""
    namevariations = []
    namevariations_match = re.search(r'<namevariations>(.*?)</namevariations>', content, re.DOTALL)
    if not namevariations_match:
        return namevariations

    namevariations_content = namevariations_match.group(1)
    extracted_variations = re.findall(r'<name>(.*?)</name>', namevariations_content, re.DOTALL)
    # 清理名称变体，去除前后空白字符
    return [variation.strip() for variation in extracted_variations if variation.strip()]

def extract_aliases(content):
    """提取aliases字段"""
    aliases = []
    aliases_match = re.search(r'<aliases>(.*?)</aliases>', content, re.DOTALL)
    if not aliases_match:
        return aliases

    aliases_content = aliases_match.group(1)

    # 使用正则表达式提取aliases
    alias_pattern = r'<name id="?(\d+)"?>(.*?)</name>'
    alias_matches = re.findall(alias_pattern, aliases_content)

    if alias_matches:
        for alias_id, alias_text in alias_matches:
            clean_text = alias_text.strip()
            if clean_text:  # 只添加非空的别名
                aliases.append({
                    'id': alias_id,
                    'name': clean_text
                })
    else:
        # 备用方法：手动解析
        alias_tags = aliases_content.split('</name>')
        for tag in alias_tags:
            if '<name id=' in tag:
                # 提取id
                id_start = tag.find('id="') + 4
                if id_start > 4:  # 确保找到了id="
                    id_end = tag.find('"', id_start)
                    if id_end > id_start:
                        alias_id = tag[id_start:id_end]

                        # 提取文本
                        text_start = tag.find('>', tag.find('<name id=')) + 1
                        if text_start > 0:
                            alias_text = tag[text_start:].strip()
                            aliases.append({
                                'id': alias_id,
                                'name': alias_text
                            })

    return aliases

def extract_groups(content):
    """提取groups字段"""
    groups = []
    groups_match = re.search(r'<groups>(.*?)</groups>', content, re.DOTALL)
    if not groups_match:
        return groups

    groups_content = groups_match.group(1)

    # 使用正则表达式提取groups
    group_pattern = r'<name id="?(\d+)"?>(.*?)</name>'
    group_matches = re.findall(group_pattern, groups_content)

    if group_matches:
        for group_id, group_text in group_matches:
            clean_text = group_text.strip()
            if clean_text:  # 只添加非空的组合名称
                groups.append({
                    'id': group_id,
                    'name': clean_text
                })
    else:
        # 备用方法：手动解析
        group_tags = groups_content.split('</name>')
        for tag in group_tags:
            if '<name id=' in tag:
                # 提取id
                id_start = tag.find('id="') + 4
                if id_start > 4:  # 确保找到了id="
                    id_end = tag.find('"', id_start)
                    if id_end > id_start:
                        group_id = tag[id_start:id_end]

                        # 提取文本
                        text_start = tag.find('>', tag.find('<name id=')) + 1
                        if text_start > 0:
                            group_text = tag[text_start:].strip()
                            groups.append({
                                'id': group_id,
                                'name': group_text
                            })
    return groups

def extract_members(content):
    """提取members字段"""
    members = []
    members_match = re.search(r'<members>(.*?)</members>', content, re.DOTALL)
    if not members_match:
        return members

    members_content = members_match.group(1)

    # 使用正则表达式提取members
    member_pattern = r'<name id="?(\d+)"?>(.*?)</name>'
    member_matches = re.findall(member_pattern, members_content)

    if member_matches:
        for member_id, member_text in member_matches:
            clean_text = member_text.strip()
            if clean_text:  # 只添加非空的成员名称
                members.append({
                    'id': member_id,
                    'name': clean_text
                })
    else:
        # 备用方法：手动解析
        member_tags = members_content.split('</name>')
        for tag in member_tags:
            if '<name id=' in tag:
                # 提取id
                id_start = tag.find('id="') + 4
                if id_start > 4:  # 确保找到了id="
                    id_end = tag.find('"', id_start)
                    if id_end > id_start:
                        member_id = tag[id_start:id_end]

                        # 提取文本
                        text_start = tag.find('>', tag.find('<name id=')) + 1
                        if text_start > 0:
                            member_text = tag[text_start:].strip()
                            members.append({
                                'id': member_id,
                                'name': member_text
                            })

    return members

def extract_sites(content):
    """提取sites字段（从urls标签中获取所有url）"""
    sites = []
    urls_match = re.search(r'<urls>(.*?)</urls>', content, re.DOTALL)
    if not urls_match:
        return sites

    urls_content = urls_match.group(1)

    # 使用正则表达式提取所有url标签的内容
    url_pattern = r'<url>(.*?)</url>'
    url_matches = re.findall(url_pattern, urls_content, re.DOTALL)

    if url_matches:
        for url_text in url_matches:
            # 清理URL，去除前后空白字符并处理HTML实体
            clean_url = url_text.strip()
            if clean_url:  # 只添加非空的URL
                # 处理HTML实体转义
                clean_url = clean_url.replace('&amp;', '&')
                sites.append(clean_url)

    return sites

def get_artist_table_by_id(db, artist_id):
    """从artist表中获取images字段"""
    try:
        # 将artist_id转换为string类型进行查询
        artist_id_str = str(artist_id)
        artist_doc = db.artist.find_one({'id': artist_id_str})
        if artist_doc and 'images' in artist_doc:
            return artist_doc
            # //['images']
        return []
    except Exception as e:
        write_output(f"获取images失败 (artist_id: {artist_id}): {e}", False)
        return []

def process_artist_content(buffer, sequential_id, db, line_number=0):
    """处理单个artist标签的内容"""
    try:
        # 提取ID
        artist_id = extract_field(buffer, 'id')
        if not artist_id:
            write_error(f"无法提取artist ID", buffer, line_number)
            return None

        # 验证ID是否为有效格式
        artist_id = artist_id.strip()
        if not artist_id:
            write_error(f"artist ID为空", buffer, line_number)
            return None

        # 记录详细的处理信息
        write_output(f"🔍 处理记录: Line {line_number}, ID={artist_id}, y_id=YA{sequential_id}", False)

        old_release_doc = get_artist_table_by_id(db, artist_id)
        # 安全地获取images字段，处理返回值可能是空列表的情况
        if isinstance(old_release_doc, dict) and 'images' in old_release_doc:
            images = old_release_doc['images']
        else:
            images = []

        # 提取其他字段，并记录提取失败的情况
        name = extract_field(buffer, 'name')
        if not name:
            write_error(f"无法提取name字段 (ID: {artist_id})", buffer, line_number)
            name = ""

        # 创建artist文档
        artist_doc = {
            'id': artist_id,
            'y_id': f"YA{sequential_id}",
            'name': name,
            'realname': extract_field(buffer, 'realname'),
            'images': images,
            'images_permissions': Permissions.ALL_VISIBLE.value,
            'profile': extract_field(buffer, 'profile'),
            'variations': extract_namevariations(buffer),
            'aliases': extract_aliases(buffer),
            'groups': extract_groups(buffer),
            'members': extract_members(buffer),
            'sites': extract_sites(buffer),
            'delete_status': DeleteStatus.NOT_DELETED.value,  # 逻辑删除状态，默认未删除
            'deleted_at': None,  # 为软删除功能准备
            'created_at': datetime.now(),
            'updated_at': datetime.now(),
            'source': Source.DISCOGS.value,
            'permissions': Permissions.ALL_VISIBLE.value,
        }

        return artist_doc

    except Exception as e:
        write_error(f"处理artist内容时出错: {e}", buffer, line_number)
        return None

def validate_xml_structure():
    """验证XML文件结构"""
    write_output("🔍 验证XML文件结构...")

    try:
        with gzip.open(XML_FILE, 'rt', encoding='utf-8') as f:
            # 读取前10KB内容进行结构验证
            sample_content = f.read(10000)

            artist_starts = sample_content.count('<artist>')
            artist_ends = sample_content.count('</artist>')

            write_output(f"📊 前10KB中发现 {artist_starts} 个<artist>标签，{artist_ends} 个</artist>标签")

            if artist_starts == 0:
                write_output("⚠️ 警告: 前10KB中未发现artist记录")
                return False

            # 检查第一个完整的artist记录
            first_artist_match = re.search(r'<artist>(.*?)</artist>', sample_content, re.DOTALL)
            if first_artist_match:
                first_artist = first_artist_match.group(1)
                id_match = re.search(r'<id>(.*?)</id>', first_artist, re.DOTALL)
                if id_match:
                    first_id = id_match.group(1).strip()
                    write_output(f"✅ 第一个artist记录ID: {first_id}")
                else:
                    write_output("⚠️ 警告: 第一个artist记录中未找到ID字段")
            else:
                write_output("⚠️ 警告: 未找到完整的artist记录")

            return True

    except Exception as e:
        write_output(f"❌ XML结构验证失败: {e}")
        return False

def process_artists():
    """处理XML文件中的artist记录"""
    write_output("开始处理artists数据...")
    write_output(f"XML文件: {XML_FILE}")
    write_output(f"数据库: {DB_NAME}")
    write_output(f"输出文件: {OUTPUT_FILE}")
    write_output(f"错误文件: {ERROR_FILE}")

    # 检查XML文件是否存在
    if not os.path.exists(XML_FILE):
        write_output(f"❌ XML文件不存在: {XML_FILE}")
        return

    # 验证XML文件结构
    if not validate_xml_structure():
        write_output("❌ XML文件结构验证失败，停止处理")
        return

    start_time = time.time()

    # 连接MongoDB
    client, db = connect_to_mongodb()

    # 确保artist_new集合存在
    if 'artist_new' not in db.list_collection_names():
        db.create_collection('artist_new')

    # 获取集合
    artists_collection = db['artists']
    artist_new_collection = db['artist_new']

    # 断点续传：不清空集合，继续添加数据
    print(f"🔄 断点续传模式：从第 {RESUME_FROM_RECORD + 1} 条记录开始处理")
    print(f"🚀 使用直接插入策略，确保数据完整性")

    processed_count = 0
    variations_count = 0
    aliases_count = 0
    groups_count = 0
    members_count = 0
    sites_count = 0
    total_records_found = 0
    skipped_count = 0  # 跳过的记录数

    # 性能统计
    insert_count = 0
    duplicate_count = 0  # 重复记录数
    parsing_errors = 0  # 解析错误数

    try:
        write_output(f"🚀 开始处理XML文件: {XML_FILE}")
        write_output(f"📊 断点续传: 从第 {RESUME_FROM_RECORD + 1} 条记录开始")
        write_output(f"⚙️ 检查点间隔: 每 {CHECKPOINT_INTERVAL} 条记录保存一次进度")
        write_output(f"📝 日志输出: 每 10 条记录显示一次详细进度")

        # 使用正则表达式方法确保不遗漏任何记录
        write_output("🔍 使用正则表达式方法确保完整性...")

        with gzip.open(XML_FILE, 'rt', encoding='utf-8') as f:
            # 分块读取文件以处理大文件
            chunk_size = 1024 * 1024  # 1MB chunks
            buffer = ""
            xml_records_found = 0

            while True:
                chunk = f.read(chunk_size)
                if not chunk:
                    break

                buffer += chunk

                # 使用正则表达式提取所有完整的artist记录
                artist_pattern = r'<artist>(.*?)</artist>'
                matches = re.findall(artist_pattern, buffer, re.DOTALL)

                # 处理找到的完整记录
                for match in matches:
                    xml_records_found += 1
                    total_records_found = xml_records_found

                    # 每1000条记录显示一次大进度
                    if xml_records_found % 1000 == 0:
                        current_time = time.time()
                        elapsed_time = current_time - start_time
                        records_per_second = xml_records_found / elapsed_time if elapsed_time > 0 else 0

                        progress_msg = (
                            f"📖 已发现 {xml_records_found:,} 个XML记录 | "
                            f"已成功处理 {processed_count:,} 条 | "
                            f"平均速度: {records_per_second:.1f} 条/秒"
                        )
                        write_output(progress_msg, True)  # 同时输出到控制台和文件

                    # 断点续传：跳过已处理的记录
                    if xml_records_found <= RESUME_FROM_RECORD:
                        skipped_count += 1
                        continue

                    # 重构完整的artist XML
                    full_artist_xml = f"<artist>{match}</artist>"

                    # 处理artist内容，使用连续的序号作为y_id
                    sequential_id = RESUME_FROM_RECORD + processed_count + 1
                    artist_doc = process_artist_content(full_artist_xml, sequential_id, db, xml_records_found)

                    if artist_doc:
                        # 查询数据库中是否存在该artist
                        existing_artist = artists_collection.find_one({'id': artist_doc['id']})

                        if existing_artist and '_id' in existing_artist:
                            # 保留原始_id
                            artist_doc['_id'] = existing_artist['_id']

                        # 使用直接插入策略
                        try:
                            operation_result = safe_insert_operation(artist_new_collection, artist_doc)

                            # 检查插入结果
                            if operation_result is None:
                                # 重复记录，跳过但记录统计
                                duplicate_count += 1
                                write_output(f"⚠️ 跳过重复记录 - ID: {artist_doc['id']}, y_id: {artist_doc['y_id']}", False)
                                continue
                            elif operation_result and operation_result.acknowledged and operation_result.inserted_id:
                                # 插入成功
                                processed_count += 1
                                variations_count += len(artist_doc['variations'])
                                aliases_count += len(artist_doc['aliases'])
                                groups_count += len(artist_doc['groups'])
                                members_count += len(artist_doc['members'])
                                sites_count += len(artist_doc['sites'])
                                insert_count += 1

                                # 每10条记录显示详细进度
                                if processed_count % 10 == 0:
                                    # 计算处理速度
                                    current_time = time.time()
                                    elapsed_time = current_time - start_time
                                    records_per_second = processed_count / elapsed_time if elapsed_time > 0 else 0

                                    # 预估剩余时间
                                    remaining_records = total_records_found - RESUME_FROM_RECORD - processed_count
                                    eta_seconds = remaining_records / records_per_second if records_per_second > 0 else 0
                                    eta_minutes = eta_seconds / 60

                                    # 格式化记录信息
                                    name_short = artist_doc['name'][:40] if artist_doc['name'] else "无名称"
                                    total_processed = RESUME_FROM_RECORD + processed_count

                                    # 详细日志信息（写入文件）
                                    log_message = (
                                        f"处理记录 {processed_count:,}: "
                                        f"y_id={artist_doc['y_id']}, "
                                        f"id={artist_doc['id']}, "
                                        f"name={name_short}, "
                                        f"variations={len(artist_doc['variations'])}, "
                                        f"aliases={len(artist_doc['aliases'])}, "
                                        f"groups={len(artist_doc['groups'])}, "
                                        f"members={len(artist_doc['members'])}, "
                                        f"sites={len(artist_doc['sites'])}"
                                    )
                                    write_output(log_message, False)

                                    # 控制台进度信息（简洁版）
                                    progress_message = (
                                        f"✅ 已处理 {processed_count:,} 条记录 "
                                        f"(总计 {total_processed:,} 条) | "
                                        f"当前: ID={artist_doc['id']}, {name_short}"
                                    )
                                    print(progress_message)
                            else:
                                write_output(f"❌ 数据插入验证失败 - ID: {artist_doc['id']}, y_id: {artist_doc['y_id']}", False)
                                continue  # 跳过这条记录，不增加processed_count

                        except Exception as e:
                            write_output(f"❌ 数据库操作异常 - ID: {artist_doc['id']}, 错误: {e}", False)
                            continue  # 跳过这条记录，不增加processed_count
                    else:
                        # 解析失败，记录错误但继续处理
                        parsing_errors += 1
                        write_error(f"解析artist记录失败 (XML记录 #{xml_records_found})", full_artist_xml, xml_records_found)
                        write_output(f"⚠️ 解析失败 - XML记录 #{xml_records_found}", False)

                    # 定期保存进度（只有在数据成功插入后才保存）
                    if processed_count % CHECKPOINT_INTERVAL == 0:
                        save_progress(processed_count, total_records_found)
                        current_time = time.time()
                        elapsed_time = current_time - start_time
                        total_processed = RESUME_FROM_RECORD + processed_count

                        checkpoint_msg = (
                            f"💾 检查点保存: 已处理 {processed_count:,} 条记录 "
                            f"(总计 {total_processed:,} 条) | "
                            f"用时: {elapsed_time/60:.1f} 分钟 | "
                            f"进度文件已更新"
                        )
                        write_output(checkpoint_msg, True)  # 同时输出到控制台和文件

                    # 达到最大处理记录数时退出（MAX_RECORDS=0表示处理全部数据）
                    if MAX_RECORDS > 0 and processed_count >= MAX_RECORDS:
                        break

                # 移除已处理的记录，保留未完成的部分
                last_complete_end = 0
                for match in re.finditer(artist_pattern, buffer, re.DOTALL):
                    last_complete_end = match.end()

                # 保留未完成的部分
                buffer = buffer[last_complete_end:]
    except Exception as e:
        error_msg = f"处理过程中出错: {e}"
        write_output(error_msg)
    finally:
        # 计算处理时间
        processing_time = time.time() - start_time

        # 输出处理结果统计
        # 计算处理速度
        records_per_second = processed_count / processing_time if processing_time > 0 else 0
        records_per_minute = records_per_second * 60

        stats = [
            "\n" + "="*60,
            "🎉 处理完成 - 详细统计报告",
            "="*60,
            f"📊 处理统计:",
            f"   本次处理了 {processed_count:,} 条记录",
            f"   跳过了 {skipped_count:,} 条记录（断点续传）",
            f"   跳过了 {duplicate_count:,} 条重复记录",
            f"   解析失败 {parsing_errors:,} 条记录",
            f"   总计已处理 {RESUME_FROM_RECORD + processed_count:,} 条记录",
            "",
            f"🔍 数据提取统计:",
            f"   提取了 {variations_count:,} 个variations",
            f"   提取了 {aliases_count:,} 个aliases",
            f"   提取了 {groups_count:,} 个groups",
            f"   提取了 {members_count:,} 个members",
            f"   提取了 {sites_count:,} 个sites",
            "",
            f"📈 性能统计:",
            f"   处理时长: {processing_time/60:.1f} 分钟 ({processing_time:.1f} 秒)",
            f"   平均速度: {records_per_second:.2f} 条/秒 ({records_per_minute:.1f} 条/分钟)",
            f"   XML记录总数: {total_records_found:,}",
            "",
            "🚀 数据库操作统计:",
            f"   成功插入操作: {insert_count:,} 条 (100.0%)" if insert_count > 0 else "   成功插入操作: 0 条",
            f"   重复记录跳过: {duplicate_count:,} 条" if duplicate_count > 0 else "   重复记录跳过: 0 条",
            "",
            "📊 数据完整性分析:",
            f"   成功处理率: {(processed_count/(total_records_found-skipped_count)*100):.2f}%" if (total_records_found-skipped_count) > 0 else "   成功处理率: 0%",
            f"   解析错误率: {(parsing_errors/(total_records_found-skipped_count)*100):.2f}%" if (total_records_found-skipped_count) > 0 else "   解析错误率: 0%",
            "",
            f"📄 日志文件: {OUTPUT_FILE}",
            f"📄 错误详情: {ERROR_FILE}" if parsing_errors > 0 else "✅ 无解析错误",
            f"📄 进度文件: {PROGRESS_FILE}"
        ]

        if MAX_RECORDS > 0 and MAX_RECORDS < total_records_found:
            stats.append(f"由于设置了最大处理记录数限制 (MAX_RECORDS={MAX_RECORDS})，只处理了部分记录")

        stats.extend([
            f"处理时长: {processing_time:.2f} 秒",
            f"平均每条记录处理时间: {processing_time/processed_count:.4f} 秒" if processed_count > 0 else "平均每条记录处理时间: 0.0000 秒",
            "="*50
        ])

        # 只打印到控制台，不写入文件
        for stat in stats:
            print(stat)

        # 关闭数据库连接
        client.close()

        # 显示输出文件内容
        print(f"\n详细输出已保存到: {OUTPUT_FILE}")

# 确保只在直接运行脚本时执行process_artists函数
if __name__ == "__main__":
    process_artists()
