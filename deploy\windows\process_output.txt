📊 数据库中最大release ID: 34419592
🔍 正在构建已存在ID缓存...
🔍 调试: ID缓存 - 原始值='1' (类型:<class 'int'>), 转换后=1 (类型:<class 'int'>)
🔍 调试: ID缓存 - 原始值='2' (类型:<class 'int'>), 转换后=2 (类型:<class 'int'>)
🔍 调试: ID缓存 - 原始值='3' (类型:<class 'int'>), 转换后=3 (类型:<class 'int'>)
🔍 调试: ID缓存 - 原始值='4' (类型:<class 'int'>), 转换后=4 (类型:<class 'int'>)
🔍 调试: ID缓存 - 原始值='5' (类型:<class 'int'>), 转换后=5 (类型:<class 'int'>)
📊 已缓存 100,000 个ID...
📊 已缓存 200,000 个ID...
📊 已缓存 300,000 个ID...
📊 已缓存 400,000 个ID...
📊 已缓存 500,000 个ID...
📊 已缓存 600,000 个ID...
📊 已缓存 700,000 个ID...
📊 已缓存 800,000 个ID...
📊 已缓存 900,000 个ID...
📊 已缓存 1,000,000 个ID...
📊 已缓存 1,100,000 个ID...
📊 已缓存 1,200,000 个ID...
📊 已缓存 1,300,000 个ID...
📊 已缓存 1,400,000 个ID...
📊 已缓存 1,500,000 个ID...
📊 已缓存 1,600,000 个ID...
📊 已缓存 1,700,000 个ID...
📊 已缓存 1,800,000 个ID...
📊 已缓存 1,900,000 个ID...
📊 已缓存 2,000,000 个ID...
📊 已缓存 2,100,000 个ID...
📊 已缓存 2,200,000 个ID...
📊 已缓存 2,300,000 个ID...
📊 已缓存 2,400,000 个ID...
📊 已缓存 2,500,000 个ID...
📊 已缓存 2,600,000 个ID...
📊 已缓存 2,700,000 个ID...
📊 已缓存 2,800,000 个ID...
📊 已缓存 2,900,000 个ID...
📊 已缓存 3,000,000 个ID...
📊 已缓存 3,100,000 个ID...
📊 已缓存 3,200,000 个ID...
📊 已缓存 3,300,000 个ID...
📊 已缓存 3,400,000 个ID...
📊 已缓存 3,500,000 个ID...
📊 已缓存 3,600,000 个ID...
📊 已缓存 3,700,000 个ID...
📊 已缓存 3,800,000 个ID...
📊 已缓存 3,900,000 个ID...
📊 已缓存 4,000,000 个ID...
📊 已缓存 4,100,000 个ID...
📊 已缓存 4,200,000 个ID...
📊 已缓存 4,300,000 个ID...
📊 已缓存 4,400,000 个ID...
📊 已缓存 4,500,000 个ID...
📊 已缓存 4,600,000 个ID...
📊 已缓存 4,700,000 个ID...
📊 已缓存 4,800,000 个ID...
📊 已缓存 4,900,000 个ID...
📊 已缓存 5,000,000 个ID...
📊 已缓存 5,100,000 个ID...
📊 已缓存 5,200,000 个ID...
📊 已缓存 5,300,000 个ID...
📊 已缓存 5,400,000 个ID...
📊 已缓存 5,500,000 个ID...
📊 已缓存 5,600,000 个ID...
📊 已缓存 5,700,000 个ID...
📊 已缓存 5,800,000 个ID...
📊 已缓存 5,900,000 个ID...
📊 已缓存 6,000,000 个ID...
📊 已缓存 6,100,000 个ID...
📊 已缓存 6,200,000 个ID...
📊 已缓存 6,300,000 个ID...
📊 已缓存 6,400,000 个ID...
📊 已缓存 6,500,000 个ID...
📊 已缓存 6,600,000 个ID...
📊 已缓存 6,700,000 个ID...
📊 已缓存 6,800,000 个ID...
📊 已缓存 6,900,000 个ID...
📊 已缓存 7,000,000 个ID...
📊 已缓存 7,100,000 个ID...
📊 已缓存 7,200,000 个ID...
📊 已缓存 7,300,000 个ID...
📊 已缓存 7,400,000 个ID...
📊 已缓存 7,500,000 个ID...
📊 已缓存 7,600,000 个ID...
📊 已缓存 7,700,000 个ID...
📊 已缓存 7,800,000 个ID...
📊 已缓存 7,900,000 个ID...
✅ ID缓存构建完成，共缓存 7,935,059 个已存在的ID
🔍 调试: 缓存中的前10个ID: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
开始处理XML文件: data\discogs_20250701_releases.xml.gz
🔍 调试: 智能补全检查 - ID=1, 在缓存中=True
⏭️ 调试: 跳过已存在记录 - ID=1, 跳过总数=1
🔍 调试: 智能补全检查 - ID=2, 在缓存中=True
⏭️ 调试: 跳过已存在记录 - ID=2, 跳过总数=2
🔍 调试: 智能补全检查 - ID=3, 在缓存中=True
⏭️ 调试: 跳过已存在记录 - ID=3, 跳过总数=3
🔍 调试: 智能补全检查 - ID=4, 在缓存中=True
⏭️ 调试: 跳过已存在记录 - ID=4, 跳过总数=4
🔍 调试: 智能补全检查 - ID=5, 在缓存中=True
⏭️ 调试: 跳过已存在记录 - ID=5, 跳过总数=5
🔍 调试: 智能补全检查 - ID=6, 在缓存中=True
🔍 调试: 智能补全检查 - ID=7, 在缓存中=True
🔍 调试: 智能补全检查 - ID=8, 在缓存中=True
🔍 调试: 智能补全检查 - ID=9, 在缓存中=True
🔍 调试: 智能补全检查 - ID=10, 在缓存中=True
📖 已扫描 100,000 条记录...
⏭️ 已跳过 100,000 条已存在记录...
📖 已扫描 200,000 条记录...
📊 全局进度: 已扫描 249,905 条记录 | 已处理 0 条 | 跳过 193,583 条 | 用时 12.4 分钟
⏭️ 已跳过 200,000 条已存在记录...
📖 已扫描 300,000 条记录...
⏭️ 已跳过 300,000 条已存在记录...
📖 已扫描 400,000 条记录...
📖 已扫描 500,000 条记录...
📊 全局进度: 已扫描 517,171 条记录 | 已处理 0 条 | 跳过 381,451 条 | 用时 12.9 分钟
⏭️ 已跳过 400,000 条已存在记录...
📖 已扫描 600,000 条记录...
⏭️ 已跳过 500,000 条已存在记录...
📖 已扫描 700,000 条记录...
📖 已扫描 800,000 条记录...
📊 全局进度: 已扫描 807,915 条记录 | 已处理 0 条 | 跳过 568,537 条 | 用时 13.4 分钟
⏭️ 已跳过 600,000 条已存在记录...
📖 已扫描 900,000 条记录...
📖 已扫描 1,000,000 条记录...
⏭️ 已跳过 700,000 条已存在记录...
📖 已扫描 1,100,000 条记录...
📊 全局进度: 已扫描 1,155,105 条记录 | 已处理 0 条 | 跳过 779,537 条 | 用时 13.9 分钟
⏭️ 已跳过 800,000 条已存在记录...
📖 已扫描 1,200,000 条记录...
📖 已扫描 1,300,000 条记录...
⏭️ 已跳过 900,000 条已存在记录...
📖 已扫描 1,400,000 条记录...
📖 已扫描 1,500,000 条记录...
📊 全局进度: 已扫描 1,508,445 条记录 | 已处理 0 条 | 跳过 989,304 条 | 用时 14.4 分钟
⏭️ 已跳过 1,000,000 条已存在记录...
📖 已扫描 1,600,000 条记录...
⏭️ 已跳过 1,100,000 条已存在记录...
📖 已扫描 1,700,000 条记录...
📖 已扫描 1,800,000 条记录...
⏭️ 已跳过 1,200,000 条已存在记录...
📊 全局进度: 已扫描 1,875,377 条记录 | 已处理 0 条 | 跳过 1,200,714 条 | 用时 14.9 分钟
📖 已扫描 1,900,000 条记录...
📖 已扫描 2,000,000 条记录...
⏭️ 已跳过 1,300,000 条已存在记录...
📖 已扫描 2,100,000 条记录...
📖 已扫描 2,200,000 条记录...
⏭️ 已跳过 1,400,000 条已存在记录...
📊 全局进度: 已扫描 2,250,650 条记录 | 已处理 0 条 | 跳过 1,407,846 条 | 用时 15.4 分钟
📖 已扫描 2,300,000 条记录...
📖 已扫描 2,400,000 条记录...
⏭️ 已跳过 1,500,000 条已存在记录...
📖 已扫描 2,500,000 条记录...
📖 已扫描 2,600,000 条记录...
⏭️ 已跳过 1,600,000 条已存在记录...
📊 全局进度: 已扫描 2,639,335 条记录 | 已处理 0 条 | 跳过 1,616,730 条 | 用时 15.9 分钟
📖 已扫描 2,700,000 条记录...
⏭️ 已跳过 1,700,000 条已存在记录...
📖 已扫描 2,800,000 条记录...
📖 已扫描 2,900,000 条记录...
⏭️ 已跳过 1,800,000 条已存在记录...
📖 已扫描 3,000,000 条记录...
📊 全局进度: 已扫描 3,034,582 条记录 | 已处理 0 条 | 跳过 1,820,555 条 | 用时 16.4 分钟
📖 已扫描 3,100,000 条记录...
⏭️ 已跳过 1,900,000 条已存在记录...
📖 已扫描 3,200,000 条记录...
📖 已扫描 3,300,000 条记录...
⏭️ 已跳过 2,000,000 条已存在记录...
📖 已扫描 3,400,000 条记录...
📊 全局进度: 已扫描 3,447,115 条记录 | 已处理 0 条 | 跳过 2,026,970 条 | 用时 16.9 分钟
📖 已扫描 3,500,000 条记录...
⏭️ 已跳过 2,100,000 条已存在记录...
📖 已扫描 3,600,000 条记录...
📖 已扫描 3,700,000 条记录...
📖 已扫描 3,800,000 条记录...
⏭️ 已跳过 2,200,000 条已存在记录...
📊 全局进度: 已扫描 3,867,597 条记录 | 已处理 0 条 | 跳过 2,232,248 条 | 用时 17.4 分钟
📖 已扫描 3,900,000 条记录...
📖 已扫描 4,000,000 条记录...
⏭️ 已跳过 2,300,000 条已存在记录...
📖 已扫描 4,100,000 条记录...
📖 已扫描 4,200,000 条记录...
⏭️ 已跳过 2,400,000 条已存在记录...
📊 全局进度: 已扫描 4,299,213 条记录 | 已处理 0 条 | 跳过 2,439,123 条 | 用时 17.9 分钟
📖 已扫描 4,300,000 条记录...
📖 已扫描 4,400,000 条记录...
⏭️ 已跳过 2,500,000 条已存在记录...
📖 已扫描 4,500,000 条记录...
📖 已扫描 4,600,000 条记录...
⏭️ 已跳过 2,600,000 条已存在记录...
📊 全局进度: 已扫描 4,678,628 条记录 | 已处理 0 条 | 跳过 2,617,092 条 | 用时 18.4 分钟
📖 已扫描 4,700,000 条记录...
📖 已扫描 4,800,000 条记录...
⏭️ 已跳过 2,700,000 条已存在记录...
📖 已扫描 4,900,000 条记录...
📖 已扫描 5,000,000 条记录...
⏭️ 已跳过 2,800,000 条已存在记录...
📖 已扫描 5,100,000 条记录...
📊 全局进度: 已扫描 5,122,963 条记录 | 已处理 0 条 | 跳过 2,820,872 条 | 用时 18.9 分钟
📖 已扫描 5,200,000 条记录...
⏭️ 已跳过 2,900,000 条已存在记录...
📖 已扫描 5,300,000 条记录...
📖 已扫描 5,400,000 条记录...
📖 已扫描 5,500,000 条记录...
⏭️ 已跳过 3,000,000 条已存在记录...
📊 全局进度: 已扫描 5,566,526 条记录 | 已处理 0 条 | 跳过 3,019,602 条 | 用时 19.4 分钟
📖 已扫描 5,600,000 条记录...
📖 已扫描 5,700,000 条记录...
⏭️ 已跳过 3,100,000 条已存在记录...
📖 已扫描 5,800,000 条记录...
📖 已扫描 5,900,000 条记录...
⏭️ 已跳过 3,200,000 条已存在记录...
📖 已扫描 6,000,000 条记录...
📊 全局进度: 已扫描 6,011,432 条记录 | 已处理 0 条 | 跳过 3,219,361 条 | 用时 19.9 分钟
📖 已扫描 6,100,000 条记录...
⏭️ 已跳过 3,300,000 条已存在记录...
📖 已扫描 6,200,000 条记录...
📖 已扫描 6,300,000 条记录...
📖 已扫描 6,400,000 条记录...
⏭️ 已跳过 3,400,000 条已存在记录...
📊 全局进度: 已扫描 6,465,509 条记录 | 已处理 0 条 | 跳过 3,419,493 条 | 用时 20.4 分钟
📖 已扫描 6,500,000 条记录...
📖 已扫描 6,600,000 条记录...
⏭️ 已跳过 3,500,000 条已存在记录...
📖 已扫描 6,700,000 条记录...
📖 已扫描 6,800,000 条记录...
⏭️ 已跳过 3,600,000 条已存在记录...
📖 已扫描 6,900,000 条记录...
📊 全局进度: 已扫描 6,925,055 条记录 | 已处理 0 条 | 跳过 3,619,217 条 | 用时 20.9 分钟
📖 已扫描 7,000,000 条记录...
📖 已扫描 7,100,000 条记录...
⏭️ 已跳过 3,700,000 条已存在记录...
📖 已扫描 7,200,000 条记录...
📖 已扫描 7,300,000 条记录...
⏭️ 已跳过 3,800,000 条已存在记录...
📊 全局进度: 已扫描 7,385,850 条记录 | 已处理 0 条 | 跳过 3,817,066 条 | 用时 21.4 分钟
📖 已扫描 7,400,000 条记录...
📖 已扫描 7,500,000 条记录...
⏭️ 已跳过 3,900,000 条已存在记录...
📖 已扫描 7,600,000 条记录...
📖 已扫描 7,700,000 条记录...
📖 已扫描 7,800,000 条记录...
⏭️ 已跳过 4,000,000 条已存在记录...
📊 全局进度: 已扫描 7,849,544 条记录 | 已处理 0 条 | 跳过 4,012,144 条 | 用时 21.9 分钟
📖 已扫描 7,900,000 条记录...
📖 已扫描 8,000,000 条记录...
⏭️ 已跳过 4,100,000 条已存在记录...
📖 已扫描 8,100,000 条记录...
📖 已扫描 8,200,000 条记录...
📖 已扫描 8,300,000 条记录...
⏭️ 已跳过 4,200,000 条已存在记录...
📊 全局进度: 已扫描 8,329,386 条记录 | 已处理 0 条 | 跳过 4,209,646 条 | 用时 22.4 分钟
📖 已扫描 8,400,000 条记录...
📖 已扫描 8,500,000 条记录...
⏭️ 已跳过 4,300,000 条已存在记录...
📖 已扫描 8,600,000 条记录...
📖 已扫描 8,700,000 条记录...
⏭️ 已跳过 4,400,000 条已存在记录...
📖 已扫描 8,800,000 条记录...
📊 全局进度: 已扫描 8,807,242 条记录 | 已处理 0 条 | 跳过 4,403,630 条 | 用时 22.9 分钟
📖 已扫描 8,900,000 条记录...
📖 已扫描 9,000,000 条记录...
⏭️ 已跳过 4,500,000 条已存在记录...
📖 已扫描 9,100,000 条记录...
📖 已扫描 9,200,000 条记录...
📊 全局进度: 已扫描 9,284,728 条记录 | 已处理 0 条 | 跳过 4,595,044 条 | 用时 23.4 分钟
⏭️ 已跳过 4,600,000 条已存在记录...
📖 已扫描 9,300,000 条记录...
📖 已扫描 9,400,000 条记录...
📖 已扫描 9,500,000 条记录...
⏭️ 已跳过 4,700,000 条已存在记录...
📖 已扫描 9,600,000 条记录...
📖 已扫描 9,700,000 条记录...
📊 全局进度: 已扫描 9,774,448 条记录 | 已处理 0 条 | 跳过 4,789,986 条 | 用时 23.9 分钟
⏭️ 已跳过 4,800,000 条已存在记录...
📖 已扫描 9,800,000 条记录...
📖 已扫描 9,900,000 条记录...
📖 已扫描 10,000,000 条记录...
⏭️ 已跳过 4,900,000 条已存在记录...
📖 已扫描 10,100,000 条记录...
📖 已扫描 10,200,000 条记录...
📊 全局进度: 已扫描 10,262,914 条记录 | 已处理 0 条 | 跳过 4,980,846 条 | 用时 24.4 分钟
📖 已扫描 10,300,000 条记录...
⏭️ 已跳过 5,000,000 条已存在记录...
📖 已扫描 10,400,000 条记录...
📖 已扫描 10,500,000 条记录...
⏭️ 已跳过 5,100,000 条已存在记录...
📖 已扫描 10,600,000 条记录...
📖 已扫描 10,700,000 条记录...
📊 全局进度: 已扫描 10,754,051 条记录 | 已处理 0 条 | 跳过 5,172,975 条 | 用时 24.9 分钟
📖 已扫描 10,800,000 条记录...
⏭️ 已跳过 5,200,000 条已存在记录...
📖 已扫描 10,900,000 条记录...
📖 已扫描 11,000,000 条记录...
⏭️ 已跳过 5,300,000 条已存在记录...
📖 已扫描 11,100,000 条记录...
📖 已扫描 11,200,000 条记录...
📊 全局进度: 已扫描 11,234,901 条记录 | 已处理 0 条 | 跳过 5,362,700 条 | 用时 25.4 分钟
📖 已扫描 11,300,000 条记录...
⏭️ 已跳过 5,400,000 条已存在记录...
📖 已扫描 11,400,000 条记录...
📖 已扫描 11,500,000 条记录...
⏭️ 已跳过 5,500,000 条已存在记录...
📖 已扫描 11,600,000 条记录...
📖 已扫描 11,700,000 条记录...
📊 全局进度: 已扫描 11,726,822 条记录 | 已处理 0 条 | 跳过 5,549,736 条 | 用时 25.9 分钟
📖 已扫描 11,800,000 条记录...
⏭️ 已跳过 5,600,000 条已存在记录...
📖 已扫描 11,900,000 条记录...
📖 已扫描 12,000,000 条记录...
📖 已扫描 12,100,000 条记录...
⏭️ 已跳过 5,700,000 条已存在记录...
📖 已扫描 12,200,000 条记录...
📊 全局进度: 已扫描 12,207,670 条记录 | 已处理 0 条 | 跳过 5,733,719 条 | 用时 26.4 分钟
📖 已扫描 12,300,000 条记录...
⏭️ 已跳过 5,800,000 条已存在记录...
📖 已扫描 12,400,000 条记录...
📖 已扫描 12,500,000 条记录...
📖 已扫描 12,600,000 条记录...
⏭️ 已跳过 5,900,000 条已存在记录...
📖 已扫描 12,700,000 条记录...
📊 全局进度: 已扫描 12,713,892 条记录 | 已处理 0 条 | 跳过 5,921,167 条 | 用时 26.9 分钟
📖 已扫描 12,800,000 条记录...
📖 已扫描 12,900,000 条记录...
⏭️ 已跳过 6,000,000 条已存在记录...
📖 已扫描 13,000,000 条记录...
📖 已扫描 13,100,000 条记录...
⏭️ 已跳过 6,100,000 条已存在记录...
📖 已扫描 13,200,000 条记录...
📊 全局进度: 已扫描 13,204,630 条记录 | 已处理 0 条 | 跳过 6,106,699 条 | 用时 27.4 分钟
📖 已扫描 13,300,000 条记录...
📖 已扫描 13,400,000 条记录...
⏭️ 已跳过 6,200,000 条已存在记录...
📖 已扫描 13,500,000 条记录...
📖 已扫描 13,600,000 条记录...
📊 全局进度: 已扫描 13,691,792 条记录 | 已处理 0 条 | 跳过 6,293,728 条 | 用时 27.9 分钟
📖 已扫描 13,700,000 条记录...
⏭️ 已跳过 6,300,000 条已存在记录...
📖 已扫描 13,800,000 条记录...
📖 已扫描 13,900,000 条记录...
⏭️ 已跳过 6,400,000 条已存在记录...
📖 已扫描 14,000,000 条记录...
📖 已扫描 14,100,000 条记录...
📊 全局进度: 已扫描 14,185,050 条记录 | 已处理 0 条 | 跳过 6,477,319 条 | 用时 28.4 分钟
📖 已扫描 14,200,000 条记录...
⏭️ 已跳过 6,500,000 条已存在记录...
📖 已扫描 14,300,000 条记录...
📖 已扫描 14,400,000 条记录...
📖 已扫描 14,500,000 条记录...
⏭️ 已跳过 6,600,000 条已存在记录...
📖 已扫描 14,600,000 条记录...
📊 全局进度: 已扫描 14,669,284 条记录 | 已处理 0 条 | 跳过 6,660,886 条 | 用时 28.9 分钟
📖 已扫描 14,700,000 条记录...
⏭️ 已跳过 6,700,000 条已存在记录...
📖 已扫描 14,800,000 条记录...
📖 已扫描 14,900,000 条记录...
📖 已扫描 15,000,000 条记录...
⏭️ 已跳过 6,800,000 条已存在记录...
📖 已扫描 15,100,000 条记录...
📊 全局进度: 已扫描 15,169,617 条记录 | 已处理 0 条 | 跳过 6,845,790 条 | 用时 29.4 分钟
📖 已扫描 15,200,000 条记录...
📖 已扫描 15,300,000 条记录...
⏭️ 已跳过 6,900,000 条已存在记录...
📖 已扫描 15,400,000 条记录...
📖 已扫描 15,500,000 条记录...
⏭️ 已跳过 7,000,000 条已存在记录...
📖 已扫描 15,600,000 条记录...
📊 全局进度: 已扫描 15,670,625 条记录 | 已处理 0 条 | 跳过 7,030,269 条 | 用时 29.9 分钟
📖 已扫描 15,700,000 条记录...
📖 已扫描 15,800,000 条记录...
⏭️ 已跳过 7,100,000 条已存在记录...
📖 已扫描 15,900,000 条记录...
📖 已扫描 16,000,000 条记录...
📖 已扫描 16,100,000 条记录...
⏭️ 已跳过 7,200,000 条已存在记录...
📊 全局进度: 已扫描 16,180,001 条记录 | 已处理 0 条 | 跳过 7,216,557 条 | 用时 30.4 分钟
📖 已扫描 16,200,000 条记录...
📖 已扫描 16,300,000 条记录...
📖 已扫描 16,400,000 条记录...
⏭️ 已跳过 7,300,000 条已存在记录...
📖 已扫描 16,500,000 条记录...
📖 已扫描 16,600,000 条记录...
📊 全局进度: 已扫描 16,696,161 条记录 | 已处理 0 条 | 跳过 7,398,074 条 | 用时 30.9 分钟
📖 已扫描 16,700,000 条记录...
⏭️ 已跳过 7,400,000 条已存在记录...
📖 已扫描 16,800,000 条记录...
📖 已扫描 16,900,000 条记录...
⏭️ 已跳过 7,500,000 条已存在记录...
📖 已扫描 17,000,000 条记录...
📖 已扫描 17,100,000 条记录...
📖 已扫描 17,200,000 条记录...
📊 全局进度: 已扫描 17,228,305 条记录 | 已处理 0 条 | 跳过 7,576,231 条 | 用时 31.4 分钟
📖 已扫描 17,300,000 条记录...
⏭️ 已跳过 7,600,000 条已存在记录...
📖 已扫描 17,400,000 条记录...
📖 已扫描 17,500,000 条记录...
📖 已扫描 17,600,000 条记录...
⏭️ 已跳过 7,700,000 条已存在记录...
📖 已扫描 17,700,000 条记录...
📊 全局进度: 已扫描 17,766,166 条记录 | 已处理 0 条 | 跳过 7,753,770 条 | 用时 31.9 分钟
📖 已扫描 17,800,000 条记录...
📖 已扫描 17,900,000 条记录...
⏭️ 已跳过 7,800,000 条已存在记录...
📖 已扫描 18,000,000 条记录...
📖 已扫描 18,100,000 条记录...
📖 已扫描 18,200,000 条记录...
⏭️ 已跳过 7,900,000 条已存在记录...
📖 已扫描 18,300,000 条记录...
📊 全局进度: 已扫描 18,318,527 条记录 | 已处理 0 条 | 跳过 7,930,695 条 | 用时 32.4 分钟

============================================================
📊 RELEASES 智能补全处理报告
============================================================

🎯 处理概览:
  • 本次新增记录: 0 条
  • XML文件总记录: 18,333,401 条
  • 跳过已存在记录: 7,935,059 条
  • 实际尝试处理: 10,398,342 条记录
  • 成功率: 0.00%

📈 智能补全统计:
  • 成功插入新记录: 0 条
  • 成功更新记录: 0 条
  • 总成功记录: 0 条

📋 处理结果分类:

⏱️ 性能指标:
  • 处理时长: 1943.51 秒 (32.4 分钟)
  • 平均处理速度: 0 秒/记录
  • 智能补全模式：检查所有记录
  • 未处理任何记录
  • 起始yId: YRD34419593

📁 日志文件:
  • 详细日志: process_output.txt
  • 无错误日志

✅ 数据完整性检查:
  ✅ 所有尝试处理的记录都已成功处理

============================================================

详细输出已保存到: process_output.txt
