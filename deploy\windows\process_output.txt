📊 数据库中最大release ID: 34419592
🔍 正在构建已存在ID缓存（优化版本）...
📊 数据库中共有 7,935,059 条记录，开始分页查询...
🔍 调试: ID缓存 - 原始值='1' (类型:<class 'int'>), 转换后=1 (类型:<class 'int'>)
🔍 调试: ID缓存 - 原始值='2' (类型:<class 'int'>), 转换后=2 (类型:<class 'int'>)
🔍 调试: ID缓存 - 原始值='3' (类型:<class 'int'>), 转换后=3 (类型:<class 'int'>)
🔍 调试: ID缓存 - 原始值='4' (类型:<class 'int'>), 转换后=4 (类型:<class 'int'>)
🔍 调试: ID缓存 - 原始值='5' (类型:<class 'int'>), 转换后=5 (类型:<class 'int'>)
📊 第1页: 处理 10000 条记录，累计 10,000/7,935,059 (0.1%)，用时 39.1s
📊 第2页: 处理 10000 条记录，累计 20,000/7,935,059 (0.3%)，用时 39.7s
📊 第3页: 处理 10000 条记录，累计 30,000/7,935,059 (0.4%)，用时 40.1s
📊 第4页: 处理 10000 条记录，累计 40,000/7,935,059 (0.5%)，用时 40.5s
📊 第5页: 处理 10000 条记录，累计 50,000/7,935,059 (0.6%)，用时 40.9s
📊 第6页: 处理 10000 条记录，累计 60,000/7,935,059 (0.8%)，用时 41.4s
📊 第7页: 处理 10000 条记录，累计 70,000/7,935,059 (0.9%)，用时 41.8s
📊 第8页: 处理 10000 条记录，累计 80,000/7,935,059 (1.0%)，用时 42.3s
📊 第9页: 处理 10000 条记录，累计 90,000/7,935,059 (1.1%)，用时 42.9s
📊 第10页: 处理 10000 条记录，累计 100,000/7,935,059 (1.3%)，用时 43.4s
📊 第11页: 处理 10000 条记录，累计 110,000/7,935,059 (1.4%)，用时 44.1s
📊 第12页: 处理 10000 条记录，累计 120,000/7,935,059 (1.5%)，用时 44.7s
📊 第13页: 处理 10000 条记录，累计 130,000/7,935,059 (1.6%)，用时 45.4s
📊 第14页: 处理 10000 条记录，累计 140,000/7,935,059 (1.8%)，用时 46.0s
📊 第15页: 处理 10000 条记录，累计 150,000/7,935,059 (1.9%)，用时 47.0s
📊 第16页: 处理 10000 条记录，累计 160,000/7,935,059 (2.0%)，用时 47.6s
📊 第17页: 处理 10000 条记录，累计 170,000/7,935,059 (2.1%)，用时 48.3s
📊 第18页: 处理 10000 条记录，累计 180,000/7,935,059 (2.3%)，用时 48.6s
📊 第19页: 处理 10000 条记录，累计 190,000/7,935,059 (2.4%)，用时 49.1s
📊 第20页: 处理 10000 条记录，累计 200,000/7,935,059 (2.5%)，用时 49.4s
📊 第21页: 处理 10000 条记录，累计 210,000/7,935,059 (2.6%)，用时 50.2s
📊 第22页: 处理 10000 条记录，累计 220,000/7,935,059 (2.8%)，用时 51.0s
📊 第23页: 处理 10000 条记录，累计 230,000/7,935,059 (2.9%)，用时 51.9s
📊 第24页: 处理 10000 条记录，累计 240,000/7,935,059 (3.0%)，用时 52.9s
📊 第25页: 处理 10000 条记录，累计 250,000/7,935,059 (3.2%)，用时 53.7s
📊 第26页: 处理 10000 条记录，累计 260,000/7,935,059 (3.3%)，用时 54.4s
📊 第27页: 处理 10000 条记录，累计 270,000/7,935,059 (3.4%)，用时 55.1s
📊 第28页: 处理 10000 条记录，累计 280,000/7,935,059 (3.5%)，用时 56.0s
📊 第29页: 处理 10000 条记录，累计 290,000/7,935,059 (3.7%)，用时 56.9s
📊 第30页: 处理 10000 条记录，累计 300,000/7,935,059 (3.8%)，用时 57.5s
📊 第31页: 处理 10000 条记录，累计 310,000/7,935,059 (3.9%)，用时 58.2s
📊 第32页: 处理 10000 条记录，累计 320,000/7,935,059 (4.0%)，用时 58.8s
📊 第33页: 处理 10000 条记录，累计 330,000/7,935,059 (4.2%)，用时 59.4s
📊 第34页: 处理 10000 条记录，累计 340,000/7,935,059 (4.3%)，用时 59.9s
📊 第35页: 处理 10000 条记录，累计 350,000/7,935,059 (4.4%)，用时 60.6s
📊 第36页: 处理 10000 条记录，累计 360,000/7,935,059 (4.5%)，用时 61.4s
📊 第37页: 处理 10000 条记录，累计 370,000/7,935,059 (4.7%)，用时 62.3s
📊 第38页: 处理 10000 条记录，累计 380,000/7,935,059 (4.8%)，用时 63.2s
📊 第39页: 处理 10000 条记录，累计 390,000/7,935,059 (4.9%)，用时 63.8s
📊 第40页: 处理 10000 条记录，累计 400,000/7,935,059 (5.0%)，用时 64.5s
📊 第41页: 处理 10000 条记录，累计 410,000/7,935,059 (5.2%)，用时 65.2s
📊 第42页: 处理 10000 条记录，累计 420,000/7,935,059 (5.3%)，用时 66.6s
📊 第43页: 处理 10000 条记录，累计 430,000/7,935,059 (5.4%)，用时 67.5s
📊 第44页: 处理 10000 条记录，累计 440,000/7,935,059 (5.5%)，用时 68.3s
📊 第45页: 处理 10000 条记录，累计 450,000/7,935,059 (5.7%)，用时 69.1s
📊 第46页: 处理 10000 条记录，累计 460,000/7,935,059 (5.8%)，用时 70.0s
📊 第47页: 处理 10000 条记录，累计 470,000/7,935,059 (5.9%)，用时 71.1s
📊 第48页: 处理 10000 条记录，累计 480,000/7,935,059 (6.0%)，用时 72.3s
📊 第49页: 处理 10000 条记录，累计 490,000/7,935,059 (6.2%)，用时 73.4s
📊 第50页: 处理 10000 条记录，累计 500,000/7,935,059 (6.3%)，用时 74.3s
� 内存使用: 70.6 MB，已缓存 500,000 个ID
📊 第51页: 处理 10000 条记录，累计 510,000/7,935,059 (6.4%)，用时 75.4s
📊 第52页: 处理 10000 条记录，累计 520,000/7,935,059 (6.6%)，用时 76.5s
📊 第53页: 处理 10000 条记录，累计 530,000/7,935,059 (6.7%)，用时 77.4s
📊 第54页: 处理 10000 条记录，累计 540,000/7,935,059 (6.8%)，用时 78.5s
📊 第55页: 处理 10000 条记录，累计 550,000/7,935,059 (6.9%)，用时 79.4s
📊 第56页: 处理 10000 条记录，累计 560,000/7,935,059 (7.1%)，用时 80.1s
📊 第57页: 处理 10000 条记录，累计 570,000/7,935,059 (7.2%)，用时 81.0s
📊 第58页: 处理 10000 条记录，累计 580,000/7,935,059 (7.3%)，用时 81.9s
📊 第59页: 处理 10000 条记录，累计 590,000/7,935,059 (7.4%)，用时 82.5s
📊 第60页: 处理 10000 条记录，累计 600,000/7,935,059 (7.6%)，用时 83.3s
📊 第61页: 处理 10000 条记录，累计 610,000/7,935,059 (7.7%)，用时 84.4s
📊 第62页: 处理 10000 条记录，累计 620,000/7,935,059 (7.8%)，用时 85.1s
📊 第63页: 处理 10000 条记录，累计 630,000/7,935,059 (7.9%)，用时 85.9s
📊 第64页: 处理 10000 条记录，累计 640,000/7,935,059 (8.1%)，用时 86.6s
📊 第65页: 处理 10000 条记录，累计 650,000/7,935,059 (8.2%)，用时 87.3s
📊 第66页: 处理 10000 条记录，累计 660,000/7,935,059 (8.3%)，用时 88.1s
📊 第67页: 处理 10000 条记录，累计 670,000/7,935,059 (8.4%)，用时 88.7s
📊 第68页: 处理 10000 条记录，累计 680,000/7,935,059 (8.6%)，用时 89.4s
📊 第69页: 处理 10000 条记录，累计 690,000/7,935,059 (8.7%)，用时 90.1s
📊 第70页: 处理 10000 条记录，累计 700,000/7,935,059 (8.8%)，用时 90.8s
📊 第71页: 处理 10000 条记录，累计 710,000/7,935,059 (8.9%)，用时 91.4s
📊 第72页: 处理 10000 条记录，累计 720,000/7,935,059 (9.1%)，用时 92.3s
📊 第73页: 处理 10000 条记录，累计 730,000/7,935,059 (9.2%)，用时 93.1s
📊 第74页: 处理 10000 条记录，累计 740,000/7,935,059 (9.3%)，用时 93.9s
📊 第75页: 处理 10000 条记录，累计 750,000/7,935,059 (9.5%)，用时 95.2s
📊 第76页: 处理 10000 条记录，累计 760,000/7,935,059 (9.6%)，用时 96.4s
📊 第77页: 处理 10000 条记录，累计 770,000/7,935,059 (9.7%)，用时 97.2s
📊 第78页: 处理 10000 条记录，累计 780,000/7,935,059 (9.8%)，用时 98.2s
📊 第79页: 处理 10000 条记录，累计 790,000/7,935,059 (10.0%)，用时 99.2s
📊 第80页: 处理 10000 条记录，累计 800,000/7,935,059 (10.1%)，用时 100.6s
📊 第81页: 处理 10000 条记录，累计 810,000/7,935,059 (10.2%)，用时 101.3s
📊 第82页: 处理 10000 条记录，累计 820,000/7,935,059 (10.3%)，用时 102.1s
📊 第83页: 处理 10000 条记录，累计 830,000/7,935,059 (10.5%)，用时 103.0s
📊 第84页: 处理 10000 条记录，累计 840,000/7,935,059 (10.6%)，用时 103.9s
📊 第85页: 处理 10000 条记录，累计 850,000/7,935,059 (10.7%)，用时 104.8s
📊 第86页: 处理 10000 条记录，累计 860,000/7,935,059 (10.8%)，用时 105.6s
📊 第87页: 处理 10000 条记录，累计 870,000/7,935,059 (11.0%)，用时 106.4s
📊 第88页: 处理 10000 条记录，累计 880,000/7,935,059 (11.1%)，用时 107.3s
📊 第89页: 处理 10000 条记录，累计 890,000/7,935,059 (11.2%)，用时 108.2s
📊 第90页: 处理 10000 条记录，累计 900,000/7,935,059 (11.3%)，用时 110.5s
📊 第91页: 处理 10000 条记录，累计 910,000/7,935,059 (11.5%)，用时 111.7s
📊 第92页: 处理 10000 条记录，累计 920,000/7,935,059 (11.6%)，用时 113.0s
📊 第93页: 处理 10000 条记录，累计 930,000/7,935,059 (11.7%)，用时 114.2s
📊 第94页: 处理 10000 条记录，累计 940,000/7,935,059 (11.8%)，用时 115.2s
📊 第95页: 处理 10000 条记录，累计 950,000/7,935,059 (12.0%)，用时 116.2s
📊 第96页: 处理 10000 条记录，累计 960,000/7,935,059 (12.1%)，用时 117.1s
📊 第97页: 处理 10000 条记录，累计 970,000/7,935,059 (12.2%)，用时 118.0s
📊 第98页: 处理 10000 条记录，累计 980,000/7,935,059 (12.4%)，用时 119.3s
📊 第99页: 处理 10000 条记录，累计 990,000/7,935,059 (12.5%)，用时 120.7s
📊 第100页: 处理 10000 条记录，累计 1,000,000/7,935,059 (12.6%)，用时 122.3s
� 内存使用: 102.6 MB，已缓存 1,000,000 个ID
📊 第101页: 处理 10000 条记录，累计 1,010,000/7,935,059 (12.7%)，用时 123.6s
📊 第102页: 处理 10000 条记录，累计 1,020,000/7,935,059 (12.9%)，用时 124.7s
📊 第103页: 处理 10000 条记录，累计 1,030,000/7,935,059 (13.0%)，用时 125.8s
📊 第104页: 处理 10000 条记录，累计 1,040,000/7,935,059 (13.1%)，用时 126.9s
📊 第105页: 处理 10000 条记录，累计 1,050,000/7,935,059 (13.2%)，用时 128.0s
📊 第106页: 处理 10000 条记录，累计 1,060,000/7,935,059 (13.4%)，用时 128.9s
📊 第107页: 处理 10000 条记录，累计 1,070,000/7,935,059 (13.5%)，用时 129.9s
📊 第108页: 处理 10000 条记录，累计 1,080,000/7,935,059 (13.6%)，用时 130.9s
📊 第109页: 处理 10000 条记录，累计 1,090,000/7,935,059 (13.7%)，用时 131.8s
📊 第110页: 处理 10000 条记录，累计 1,100,000/7,935,059 (13.9%)，用时 132.7s
📊 第111页: 处理 10000 条记录，累计 1,110,000/7,935,059 (14.0%)，用时 133.8s
📊 第112页: 处理 10000 条记录，累计 1,120,000/7,935,059 (14.1%)，用时 135.0s
📊 第113页: 处理 10000 条记录，累计 1,130,000/7,935,059 (14.2%)，用时 135.9s
📊 第114页: 处理 10000 条记录，累计 1,140,000/7,935,059 (14.4%)，用时 136.9s
📊 第115页: 处理 10000 条记录，累计 1,150,000/7,935,059 (14.5%)，用时 137.9s
📊 第116页: 处理 10000 条记录，累计 1,160,000/7,935,059 (14.6%)，用时 138.9s
📊 第117页: 处理 10000 条记录，累计 1,170,000/7,935,059 (14.7%)，用时 141.0s
📊 第118页: 处理 10000 条记录，累计 1,180,000/7,935,059 (14.9%)，用时 142.4s
📊 第119页: 处理 10000 条记录，累计 1,190,000/7,935,059 (15.0%)，用时 143.6s
📊 第120页: 处理 10000 条记录，累计 1,200,000/7,935,059 (15.1%)，用时 145.0s
📊 第121页: 处理 10000 条记录，累计 1,210,000/7,935,059 (15.2%)，用时 146.6s
📊 第122页: 处理 10000 条记录，累计 1,220,000/7,935,059 (15.4%)，用时 148.0s
📊 第123页: 处理 10000 条记录，累计 1,230,000/7,935,059 (15.5%)，用时 149.4s
📊 第124页: 处理 10000 条记录，累计 1,240,000/7,935,059 (15.6%)，用时 150.6s
📊 第125页: 处理 10000 条记录，累计 1,250,000/7,935,059 (15.8%)，用时 151.9s
📊 第126页: 处理 10000 条记录，累计 1,260,000/7,935,059 (15.9%)，用时 153.6s
📊 第127页: 处理 10000 条记录，累计 1,270,000/7,935,059 (16.0%)，用时 154.8s
📊 第128页: 处理 10000 条记录，累计 1,280,000/7,935,059 (16.1%)，用时 155.8s
📊 第129页: 处理 10000 条记录，累计 1,290,000/7,935,059 (16.3%)，用时 157.2s
📊 第130页: 处理 10000 条记录，累计 1,300,000/7,935,059 (16.4%)，用时 158.5s
📊 第131页: 处理 10000 条记录，累计 1,310,000/7,935,059 (16.5%)，用时 159.8s
📊 第132页: 处理 10000 条记录，累计 1,320,000/7,935,059 (16.6%)，用时 160.8s
📊 第133页: 处理 10000 条记录，累计 1,330,000/7,935,059 (16.8%)，用时 162.1s
📊 第134页: 处理 10000 条记录，累计 1,340,000/7,935,059 (16.9%)，用时 163.3s
📊 第135页: 处理 10000 条记录，累计 1,350,000/7,935,059 (17.0%)，用时 164.5s
📊 第136页: 处理 10000 条记录，累计 1,360,000/7,935,059 (17.1%)，用时 165.7s
📊 第137页: 处理 10000 条记录，累计 1,370,000/7,935,059 (17.3%)，用时 166.9s
📊 第138页: 处理 10000 条记录，累计 1,380,000/7,935,059 (17.4%)，用时 168.0s
📊 第139页: 处理 10000 条记录，累计 1,390,000/7,935,059 (17.5%)，用时 169.3s
📊 第140页: 处理 10000 条记录，累计 1,400,000/7,935,059 (17.6%)，用时 170.5s
📊 第141页: 处理 10000 条记录，累计 1,410,000/7,935,059 (17.8%)，用时 171.9s
📊 第142页: 处理 10000 条记录，累计 1,420,000/7,935,059 (17.9%)，用时 173.5s
📊 第143页: 处理 10000 条记录，累计 1,430,000/7,935,059 (18.0%)，用时 174.7s
📊 第144页: 处理 10000 条记录，累计 1,440,000/7,935,059 (18.1%)，用时 176.3s
📊 第145页: 处理 10000 条记录，累计 1,450,000/7,935,059 (18.3%)，用时 177.7s
📊 第146页: 处理 10000 条记录，累计 1,460,000/7,935,059 (18.4%)，用时 179.2s
📊 第147页: 处理 10000 条记录，累计 1,470,000/7,935,059 (18.5%)，用时 180.8s
📊 第148页: 处理 10000 条记录，累计 1,480,000/7,935,059 (18.7%)，用时 182.4s
📊 第149页: 处理 10000 条记录，累计 1,490,000/7,935,059 (18.8%)，用时 183.6s
📊 第150页: 处理 10000 条记录，累计 1,500,000/7,935,059 (18.9%)，用时 185.0s
� 内存使用: 149.9 MB，已缓存 1,500,000 个ID
📊 第151页: 处理 10000 条记录，累计 1,510,000/7,935,059 (19.0%)，用时 186.6s
📊 第152页: 处理 10000 条记录，累计 1,520,000/7,935,059 (19.2%)，用时 188.4s
📊 第153页: 处理 10000 条记录，累计 1,530,000/7,935,059 (19.3%)，用时 190.0s
📊 第154页: 处理 10000 条记录，累计 1,540,000/7,935,059 (19.4%)，用时 191.4s
📊 第155页: 处理 10000 条记录，累计 1,550,000/7,935,059 (19.5%)，用时 192.9s
📊 第156页: 处理 10000 条记录，累计 1,560,000/7,935,059 (19.7%)，用时 194.5s
📊 第157页: 处理 10000 条记录，累计 1,570,000/7,935,059 (19.8%)，用时 195.9s
📊 第158页: 处理 10000 条记录，累计 1,580,000/7,935,059 (19.9%)，用时 197.2s
📊 第159页: 处理 10000 条记录，累计 1,590,000/7,935,059 (20.0%)，用时 198.7s
📊 第160页: 处理 10000 条记录，累计 1,600,000/7,935,059 (20.2%)，用时 200.1s
📊 第161页: 处理 10000 条记录，累计 1,610,000/7,935,059 (20.3%)，用时 201.3s
📊 第162页: 处理 10000 条记录，累计 1,620,000/7,935,059 (20.4%)，用时 202.5s
📊 第163页: 处理 10000 条记录，累计 1,630,000/7,935,059 (20.5%)，用时 204.0s
📊 第164页: 处理 10000 条记录，累计 1,640,000/7,935,059 (20.7%)，用时 205.4s
📊 第165页: 处理 10000 条记录，累计 1,650,000/7,935,059 (20.8%)，用时 207.0s
📊 第166页: 处理 10000 条记录，累计 1,660,000/7,935,059 (20.9%)，用时 208.4s
📊 第167页: 处理 10000 条记录，累计 1,670,000/7,935,059 (21.0%)，用时 209.7s
📊 第168页: 处理 10000 条记录，累计 1,680,000/7,935,059 (21.2%)，用时 211.1s
📊 第169页: 处理 10000 条记录，累计 1,690,000/7,935,059 (21.3%)，用时 212.3s
📊 第170页: 处理 10000 条记录，累计 1,700,000/7,935,059 (21.4%)，用时 213.6s
📊 第171页: 处理 10000 条记录，累计 1,710,000/7,935,059 (21.5%)，用时 214.9s
📊 第172页: 处理 10000 条记录，累计 1,720,000/7,935,059 (21.7%)，用时 216.5s
📊 第173页: 处理 10000 条记录，累计 1,730,000/7,935,059 (21.8%)，用时 218.0s
📊 第174页: 处理 10000 条记录，累计 1,740,000/7,935,059 (21.9%)，用时 219.5s
📊 第175页: 处理 10000 条记录，累计 1,750,000/7,935,059 (22.1%)，用时 220.8s
📊 第176页: 处理 10000 条记录，累计 1,760,000/7,935,059 (22.2%)，用时 222.0s
📊 第177页: 处理 10000 条记录，累计 1,770,000/7,935,059 (22.3%)，用时 223.5s
📊 第178页: 处理 10000 条记录，累计 1,780,000/7,935,059 (22.4%)，用时 225.6s
📊 第179页: 处理 10000 条记录，累计 1,790,000/7,935,059 (22.6%)，用时 227.0s
📊 第180页: 处理 10000 条记录，累计 1,800,000/7,935,059 (22.7%)，用时 228.4s
📊 第181页: 处理 10000 条记录，累计 1,810,000/7,935,059 (22.8%)，用时 229.6s
📊 第182页: 处理 10000 条记录，累计 1,820,000/7,935,059 (22.9%)，用时 230.9s
📊 第183页: 处理 10000 条记录，累计 1,830,000/7,935,059 (23.1%)，用时 232.3s
📊 第184页: 处理 10000 条记录，累计 1,840,000/7,935,059 (23.2%)，用时 233.7s
📊 第185页: 处理 10000 条记录，累计 1,850,000/7,935,059 (23.3%)，用时 234.9s
📊 第186页: 处理 10000 条记录，累计 1,860,000/7,935,059 (23.4%)，用时 236.2s
📊 第187页: 处理 10000 条记录，累计 1,870,000/7,935,059 (23.6%)，用时 237.9s
📊 第188页: 处理 10000 条记录，累计 1,880,000/7,935,059 (23.7%)，用时 239.4s
📊 第189页: 处理 10000 条记录，累计 1,890,000/7,935,059 (23.8%)，用时 240.8s
📊 第190页: 处理 10000 条记录，累计 1,900,000/7,935,059 (23.9%)，用时 242.3s
📊 第191页: 处理 10000 条记录，累计 1,910,000/7,935,059 (24.1%)，用时 243.8s
📊 第192页: 处理 10000 条记录，累计 1,920,000/7,935,059 (24.2%)，用时 245.4s
📊 第193页: 处理 10000 条记录，累计 1,930,000/7,935,059 (24.3%)，用时 247.0s
📊 第194页: 处理 10000 条记录，累计 1,940,000/7,935,059 (24.4%)，用时 248.3s
📊 第195页: 处理 10000 条记录，累计 1,950,000/7,935,059 (24.6%)，用时 249.6s
📊 第196页: 处理 10000 条记录，累计 1,960,000/7,935,059 (24.7%)，用时 250.9s
📊 第197页: 处理 10000 条记录，累计 1,970,000/7,935,059 (24.8%)，用时 252.4s
📊 第198页: 处理 10000 条记录，累计 1,980,000/7,935,059 (25.0%)，用时 253.9s
📊 第199页: 处理 10000 条记录，累计 1,990,000/7,935,059 (25.1%)，用时 255.6s
📊 第200页: 处理 10000 条记录，累计 2,000,000/7,935,059 (25.2%)，用时 257.3s
� 内存使用: 165.2 MB，已缓存 2,000,000 个ID
📊 第201页: 处理 10000 条记录，累计 2,010,000/7,935,059 (25.3%)，用时 258.8s
📊 第202页: 处理 10000 条记录，累计 2,020,000/7,935,059 (25.5%)，用时 260.5s
📊 第203页: 处理 10000 条记录，累计 2,030,000/7,935,059 (25.6%)，用时 262.3s
📊 第204页: 处理 10000 条记录，累计 2,040,000/7,935,059 (25.7%)，用时 264.2s
📊 第205页: 处理 10000 条记录，累计 2,050,000/7,935,059 (25.8%)，用时 265.9s
📊 第206页: 处理 10000 条记录，累计 2,060,000/7,935,059 (26.0%)，用时 267.3s
📊 第207页: 处理 10000 条记录，累计 2,070,000/7,935,059 (26.1%)，用时 269.2s
📊 第208页: 处理 10000 条记录，累计 2,080,000/7,935,059 (26.2%)，用时 270.7s
📊 第209页: 处理 10000 条记录，累计 2,090,000/7,935,059 (26.3%)，用时 272.1s
📊 第210页: 处理 10000 条记录，累计 2,100,000/7,935,059 (26.5%)，用时 273.7s
📊 第211页: 处理 10000 条记录，累计 2,110,000/7,935,059 (26.6%)，用时 275.6s
📊 第212页: 处理 10000 条记录，累计 2,120,000/7,935,059 (26.7%)，用时 277.2s
📊 第213页: 处理 10000 条记录，累计 2,130,000/7,935,059 (26.8%)，用时 278.7s
📊 第214页: 处理 10000 条记录，累计 2,140,000/7,935,059 (27.0%)，用时 280.2s
📊 第215页: 处理 10000 条记录，累计 2,150,000/7,935,059 (27.1%)，用时 281.6s
📊 第216页: 处理 10000 条记录，累计 2,160,000/7,935,059 (27.2%)，用时 283.3s
📊 第217页: 处理 10000 条记录，累计 2,170,000/7,935,059 (27.3%)，用时 284.7s
📊 第218页: 处理 10000 条记录，累计 2,180,000/7,935,059 (27.5%)，用时 286.1s
📊 第219页: 处理 10000 条记录，累计 2,190,000/7,935,059 (27.6%)，用时 287.6s
📊 第220页: 处理 10000 条记录，累计 2,200,000/7,935,059 (27.7%)，用时 289.1s
📊 第221页: 处理 10000 条记录，累计 2,210,000/7,935,059 (27.9%)，用时 290.7s
📊 第222页: 处理 10000 条记录，累计 2,220,000/7,935,059 (28.0%)，用时 292.4s
📊 第223页: 处理 10000 条记录，累计 2,230,000/7,935,059 (28.1%)，用时 293.9s
📊 第224页: 处理 10000 条记录，累计 2,240,000/7,935,059 (28.2%)，用时 295.4s
📊 第225页: 处理 10000 条记录，累计 2,250,000/7,935,059 (28.4%)，用时 296.8s
📊 第226页: 处理 10000 条记录，累计 2,260,000/7,935,059 (28.5%)，用时 298.5s
📊 第227页: 处理 10000 条记录，累计 2,270,000/7,935,059 (28.6%)，用时 300.2s
❌ ID缓存构建超时 (300秒)，已缓存 2,270,000 个ID
✅ ID缓存构建完成，共缓存 2,270,000 个已存在的ID，用时 300.2 秒
🔍 调试: 缓存中的前10个ID: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
🔍 调试: 缓存中的最后5个ID: [4590034, 4590035, 4590037, 4590038, 4590042]
开始处理XML文件: data\discogs_20250701_releases.xml.gz
🔍 调试: 缓存检查 - ID=1, 在缓存中=True
⏭️ 调试: 缓存检查跳过已存在记录 - ID=1, 跳过总数=1
🔍 调试: 缓存检查 - ID=2, 在缓存中=True
⏭️ 调试: 缓存检查跳过已存在记录 - ID=2, 跳过总数=2
🔍 调试: 缓存检查 - ID=3, 在缓存中=True
⏭️ 调试: 缓存检查跳过已存在记录 - ID=3, 跳过总数=3
🔍 调试: 缓存检查 - ID=4, 在缓存中=True
⏭️ 调试: 缓存检查跳过已存在记录 - ID=4, 跳过总数=4
🔍 调试: 缓存检查 - ID=5, 在缓存中=True
⏭️ 调试: 缓存检查跳过已存在记录 - ID=5, 跳过总数=5
🔍 调试: 缓存检查 - ID=6, 在缓存中=True
🔍 调试: 缓存检查 - ID=7, 在缓存中=True
🔍 调试: 缓存检查 - ID=8, 在缓存中=True
🔍 调试: 缓存检查 - ID=9, 在缓存中=True
🔍 调试: 缓存检查 - ID=10, 在缓存中=True
📖 已扫描 100,000 条记录...
⏭️ 缓存检查已跳过 100,000 条已存在记录...
📖 已扫描 200,000 条记录...
⏭️ 缓存检查已跳过 200,000 条已存在记录...
📊 全局进度: 已扫描 268,800 条记录 | 已处理 0 条 | 跳过 206,738 条 | 用时 8.4 分钟
📖 已扫描 300,000 条记录...
⏭️ 缓存检查已跳过 300,000 条已存在记录...
📖 已扫描 400,000 条记录...
📖 已扫描 500,000 条记录...
📊 全局进度: 已扫描 507,417 条记录 | 已处理 0 条 | 跳过 374,738 条 | 用时 8.9 分钟
⏭️ 缓存检查已跳过 400,000 条已存在记录...
📖 已扫描 600,000 条记录...
⏭️ 缓存检查已跳过 500,000 条已存在记录...
📖 已扫描 700,000 条记录...
📊 全局进度: 已扫描 775,378 条记录 | 已处理 0 条 | 跳过 548,308 条 | 用时 9.4 分钟
📖 已扫描 800,000 条记录...
⏭️ 缓存检查已跳过 600,000 条已存在记录...
📖 已扫描 900,000 条记录...
📖 已扫描 1,000,000 条记录...
⏭️ 缓存检查已跳过 700,000 条已存在记录...
📊 全局进度: 已扫描 1,097,243 条记录 | 已处理 0 条 | 跳过 744,739 条 | 用时 9.9 分钟
📖 已扫描 1,100,000 条记录...
⏭️ 缓存检查已跳过 800,000 条已存在记录...
📖 已扫描 1,200,000 条记录...
📖 已扫描 1,300,000 条记录...
⏭️ 缓存检查已跳过 900,000 条已存在记录...
📖 已扫描 1,400,000 条记录...
📊 全局进度: 已扫描 1,405,230 条记录 | 已处理 0 条 | 跳过 927,718 条 | 用时 10.4 分钟
📖 已扫描 1,500,000 条记录...
⏭️ 缓存检查已跳过 1,000,000 条已存在记录...
📖 已扫描 1,600,000 条记录...
⏭️ 缓存检查已跳过 1,100,000 条已存在记录...
📖 已扫描 1,700,000 条记录...
📊 全局进度: 已扫描 1,714,731 条记录 | 已处理 0 条 | 跳过 1,111,840 条 | 用时 10.9 分钟
📖 已扫描 1,800,000 条记录...
⏭️ 缓存检查已跳过 1,200,000 条已存在记录...
📖 已扫描 1,900,000 条记录...
📖 已扫描 2,000,000 条记录...
📊 全局进度: 已扫描 2,050,462 条记录 | 已处理 0 条 | 跳过 1,296,711 条 | 用时 11.4 分钟
⏭️ 缓存检查已跳过 1,300,000 条已存在记录...
📖 已扫描 2,100,000 条记录...
📖 已扫描 2,200,000 条记录...
⏭️ 缓存检查已跳过 1,400,000 条已存在记录...
📖 已扫描 2,300,000 条记录...
📊 全局进度: 已扫描 2,382,644 条记录 | 已处理 0 条 | 跳过 1,479,425 条 | 用时 11.9 分钟
📖 已扫描 2,400,000 条记录...
⏭️ 缓存检查已跳过 1,500,000 条已存在记录...
📖 已扫描 2,500,000 条记录...
📖 已扫描 2,600,000 条记录...
⏭️ 缓存检查已跳过 1,600,000 条已存在记录...
📖 已扫描 2,700,000 条记录...
📊 全局进度: 已扫描 2,726,879 条记录 | 已处理 0 条 | 跳过 1,662,657 条 | 用时 12.4 分钟
⏭️ 缓存检查已跳过 1,700,000 条已存在记录...
📖 已扫描 2,800,000 条记录...
📖 已扫描 2,900,000 条记录...
⏭️ 缓存检查已跳过 1,800,000 条已存在记录...
📖 已扫描 3,000,000 条记录...
📊 全局进度: 已扫描 3,079,303 条记录 | 已处理 0 条 | 跳过 1,843,279 条 | 用时 12.9 分钟
📖 已扫描 3,100,000 条记录...
⏭️ 缓存检查已跳过 1,900,000 条已存在记录...
📖 已扫描 3,200,000 条记录...
📖 已扫描 3,300,000 条记录...
⏭️ 缓存检查已跳过 2,000,000 条已存在记录...
📖 已扫描 3,400,000 条记录...
📊 全局进度: 已扫描 3,474,178 条记录 | 已处理 0 条 | 跳过 2,040,438 条 | 用时 13.4 分钟
📖 已扫描 3,500,000 条记录...
⏭️ 缓存检查已跳过 2,100,000 条已存在记录...
📖 已扫描 3,600,000 条记录...
📖 已扫描 3,700,000 条记录...
📖 已扫描 3,800,000 条记录...
⏭️ 缓存检查已跳过 2,200,000 条已存在记录...
📖 已扫描 3,900,000 条记录...
📊 全局进度: 已扫描 3,900,489 条记录 | 已处理 0 条 | 跳过 2,247,939 条 | 用时 13.9 分钟
🔍 处理记录: Line 3946842, ID=4590049, y_id=YRD34419593
获取images失败 (release_id: 4590049): ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
🔍 调试: 准备插入记录 ID=4590049, y_id=YRD34419593
🎯 找到第一个需要插入的记录: ID=4590049, y_id=YRD34419593
🔍 智能补全: 尝试插入记录 ID=4590049
❌ 智能补全: 插入操作异常 - ID: 4590049, 错误: ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
❌ 智能补全数据库操作异常 - ID: 4590049, 错误: ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
❌ 数据库操作异常 - ID: 4590049, 错误: ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
📊 全局进度: 已扫描 3,946,842 条记录 | 已处理 0 条 | 跳过 2,270,000 条 | 用时 15.0 分钟
🔍 处理记录: Line 3946843, ID=4590052, y_id=YRD34419593
获取images失败 (release_id: 4590052): ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
🔍 调试: 准备插入记录 ID=4590052, y_id=YRD34419593
🎯 找到第一个需要插入的记录: ID=4590052, y_id=YRD34419593
🔍 智能补全: 尝试插入记录 ID=4590052
❌ 智能补全: 插入操作异常 - ID: 4590052, 错误: ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
❌ 智能补全数据库操作异常 - ID: 4590052, 错误: ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
❌ 数据库操作异常 - ID: 4590052, 错误: ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
📊 全局进度: 已扫描 3,946,843 条记录 | 已处理 0 条 | 跳过 2,270,000 条 | 用时 16.0 分钟
🔍 处理记录: Line 3946845, ID=4590054, y_id=YRD34419593
获取images失败 (release_id: 4590054): ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
🔍 调试: 准备插入记录 ID=4590054, y_id=YRD34419593
🎯 找到第一个需要插入的记录: ID=4590054, y_id=YRD34419593
🔍 智能补全: 尝试插入记录 ID=4590054
❌ 智能补全: 插入操作异常 - ID: 4590054, 错误: ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
❌ 智能补全数据库操作异常 - ID: 4590054, 错误: ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
❌ 数据库操作异常 - ID: 4590054, 错误: ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
📊 全局进度: 已扫描 3,946,845 条记录 | 已处理 0 条 | 跳过 2,270,000 条 | 用时 17.0 分钟
🔍 处理记录: Line 3946848, ID=4590058, y_id=YRD34419593
获取images失败 (release_id: 4590058): ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
🔍 调试: 准备插入记录 ID=4590058, y_id=YRD34419593
🎯 找到第一个需要插入的记录: ID=4590058, y_id=YRD34419593
🔍 智能补全: 尝试插入记录 ID=4590058
❌ 智能补全: 插入操作异常 - ID: 4590058, 错误: ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
❌ 智能补全数据库操作异常 - ID: 4590058, 错误: ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
❌ 数据库操作异常 - ID: 4590058, 错误: ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
📊 全局进度: 已扫描 3,946,848 条记录 | 已处理 0 条 | 跳过 2,270,000 条 | 用时 18.0 分钟
🔍 处理记录: Line 3946849, ID=4590060, y_id=YRD34419593
获取images失败 (release_id: 4590060): ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
🔍 调试: 准备插入记录 ID=4590060, y_id=YRD34419593
🎯 找到第一个需要插入的记录: ID=4590060, y_id=YRD34419593
🔍 智能补全: 尝试插入记录 ID=4590060
❌ 智能补全: 插入操作异常 - ID: 4590060, 错误: ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
❌ 智能补全数据库操作异常 - ID: 4590060, 错误: ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
❌ 数据库操作异常 - ID: 4590060, 错误: ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
📊 全局进度: 已扫描 3,946,849 条记录 | 已处理 0 条 | 跳过 2,270,000 条 | 用时 19.0 分钟
🔍 处理记录: Line 3946852, ID=4590063, y_id=YRD34419593
获取images失败 (release_id: 4590063): ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
🔍 调试: 准备插入记录 ID=4590063, y_id=YRD34419593
🎯 找到第一个需要插入的记录: ID=4590063, y_id=YRD34419593
🔍 智能补全: 尝试插入记录 ID=4590063
❌ 智能补全: 插入操作异常 - ID: 4590063, 错误: ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
❌ 智能补全数据库操作异常 - ID: 4590063, 错误: ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
❌ 数据库操作异常 - ID: 4590063, 错误: ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
📊 全局进度: 已扫描 3,946,852 条记录 | 已处理 0 条 | 跳过 2,270,000 条 | 用时 20.0 分钟
🔍 处理记录: Line 3946855, ID=4590067, y_id=YRD34419593
获取images失败 (release_id: 4590067): ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
🔍 调试: 准备插入记录 ID=4590067, y_id=YRD34419593
🎯 找到第一个需要插入的记录: ID=4590067, y_id=YRD34419593
🔍 智能补全: 尝试插入记录 ID=4590067
❌ 智能补全: 插入操作异常 - ID: 4590067, 错误: ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
❌ 智能补全数据库操作异常 - ID: 4590067, 错误: ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
❌ 数据库操作异常 - ID: 4590067, 错误: ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
📊 全局进度: 已扫描 3,946,855 条记录 | 已处理 0 条 | 跳过 2,270,000 条 | 用时 21.0 分钟
🔍 处理记录: Line 3946856, ID=4590069, y_id=YRD34419593
获取images失败 (release_id: 4590069): ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
🔍 调试: 准备插入记录 ID=4590069, y_id=YRD34419593
🎯 找到第一个需要插入的记录: ID=4590069, y_id=YRD34419593
🔍 智能补全: 尝试插入记录 ID=4590069
❌ 智能补全: 插入操作异常 - ID: 4590069, 错误: ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
❌ 智能补全数据库操作异常 - ID: 4590069, 错误: ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
❌ 数据库操作异常 - ID: 4590069, 错误: ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
📊 全局进度: 已扫描 3,946,856 条记录 | 已处理 0 条 | 跳过 2,270,000 条 | 用时 22.0 分钟
🔍 处理记录: Line 3946858, ID=4590071, y_id=YRD34419593
获取images失败 (release_id: 4590071): ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
🔍 调试: 准备插入记录 ID=4590071, y_id=YRD34419593
🎯 找到第一个需要插入的记录: ID=4590071, y_id=YRD34419593
🔍 智能补全: 尝试插入记录 ID=4590071
❌ 智能补全: 插入操作异常 - ID: 4590071, 错误: ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
❌ 智能补全数据库操作异常 - ID: 4590071, 错误: ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
❌ 数据库操作异常 - ID: 4590071, 错误: ************:27017: timed out,************:27018: timed out,************:27017: timed out, Timeout: 30s, Topology Description: <TopologyDescription id: 6886e28d0dbdd91a8a2dd8a8, topology_type: ReplicaSetNoPrimary, servers: [<ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27017) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27017: timed out')>, <ServerDescription ('************', 27018) server_type: Unknown, rtt: None, error=NetworkTimeout('************:27018: timed out')>]>
📊 全局进度: 已扫描 3,946,858 条记录 | 已处理 0 条 | 跳过 2,270,000 条 | 用时 23.1 分钟
🔍 处理记录: Line 3946859, ID=4590072, y_id=YRD34419593
