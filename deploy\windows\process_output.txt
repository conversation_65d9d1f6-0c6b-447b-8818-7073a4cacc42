📊 数据库中最大release ID: 34419592
🔍 正在构建已存在ID缓存...
🔍 调试: ID缓存 - 原始值='1' (类型:<class 'int'>), 转换后=1 (类型:<class 'int'>)
🔍 调试: ID缓存 - 原始值='2' (类型:<class 'int'>), 转换后=2 (类型:<class 'int'>)
🔍 调试: ID缓存 - 原始值='3' (类型:<class 'int'>), 转换后=3 (类型:<class 'int'>)
🔍 调试: ID缓存 - 原始值='4' (类型:<class 'int'>), 转换后=4 (类型:<class 'int'>)
🔍 调试: ID缓存 - 原始值='5' (类型:<class 'int'>), 转换后=5 (类型:<class 'int'>)
📊 已缓存 100,000 个ID...
📊 已缓存 200,000 个ID...
📊 已缓存 300,000 个ID...
📊 已缓存 400,000 个ID...
📊 已缓存 500,000 个ID...
📊 已缓存 600,000 个ID...
📊 已缓存 700,000 个ID...
📊 已缓存 800,000 个ID...
📊 已缓存 900,000 个ID...
📊 已缓存 1,000,000 个ID...
📊 已缓存 1,100,000 个ID...
📊 已缓存 1,200,000 个ID...
📊 已缓存 1,300,000 个ID...
📊 已缓存 1,400,000 个ID...
📊 已缓存 1,500,000 个ID...
📊 已缓存 1,600,000 个ID...
📊 已缓存 1,700,000 个ID...
📊 已缓存 1,800,000 个ID...
📊 已缓存 1,900,000 个ID...
📊 已缓存 2,000,000 个ID...
📊 已缓存 2,100,000 个ID...
📊 已缓存 2,200,000 个ID...
📊 已缓存 2,300,000 个ID...
📊 已缓存 2,400,000 个ID...
📊 已缓存 2,500,000 个ID...
📊 已缓存 2,600,000 个ID...
📊 已缓存 2,700,000 个ID...
📊 已缓存 2,800,000 个ID...
📊 已缓存 2,900,000 个ID...
📊 已缓存 3,000,000 个ID...
📊 已缓存 3,100,000 个ID...
📊 已缓存 3,200,000 个ID...
📊 已缓存 3,300,000 个ID...
📊 已缓存 3,400,000 个ID...
📊 已缓存 3,500,000 个ID...
📊 已缓存 3,600,000 个ID...
📊 已缓存 3,700,000 个ID...
📊 已缓存 3,800,000 个ID...
📊 已缓存 3,900,000 个ID...
📊 已缓存 4,000,000 个ID...
📊 已缓存 4,100,000 个ID...
📊 已缓存 4,200,000 个ID...
📊 已缓存 4,300,000 个ID...
📊 已缓存 4,400,000 个ID...
📊 已缓存 4,500,000 个ID...
📊 已缓存 4,600,000 个ID...
📊 已缓存 4,700,000 个ID...
📊 已缓存 4,800,000 个ID...
📊 已缓存 4,900,000 个ID...
📊 已缓存 5,000,000 个ID...
📊 已缓存 5,100,000 个ID...
📊 已缓存 5,200,000 个ID...
📊 已缓存 5,300,000 个ID...
📊 已缓存 5,400,000 个ID...
📊 已缓存 5,500,000 个ID...
📊 已缓存 5,600,000 个ID...
📊 已缓存 5,700,000 个ID...
📊 已缓存 5,800,000 个ID...
📊 已缓存 5,900,000 个ID...
📊 已缓存 6,000,000 个ID...
📊 已缓存 6,100,000 个ID...
📊 已缓存 6,200,000 个ID...
📊 已缓存 6,300,000 个ID...
📊 已缓存 6,400,000 个ID...
📊 已缓存 6,500,000 个ID...
📊 已缓存 6,600,000 个ID...
📊 已缓存 6,700,000 个ID...
📊 已缓存 6,800,000 个ID...
📊 已缓存 6,900,000 个ID...
📊 已缓存 7,000,000 个ID...
📊 已缓存 7,100,000 个ID...
📊 已缓存 7,200,000 个ID...
📊 已缓存 7,300,000 个ID...
📊 已缓存 7,400,000 个ID...
📊 已缓存 7,500,000 个ID...
📊 已缓存 7,600,000 个ID...
📊 已缓存 7,700,000 个ID...
📊 已缓存 7,800,000 个ID...
📊 已缓存 7,900,000 个ID...
✅ ID缓存构建完成，共缓存 7,935,059 个已存在的ID
🔍 调试: 缓存中的前10个ID: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
开始处理XML文件: data\discogs_20250701_releases.xml.gz
🔍 调试: 智能补全检查 - ID=1, 在缓存中=True
⏭️ 调试: 跳过已存在记录 - ID=1, 跳过总数=1
🔍 调试: 智能补全检查 - ID=2, 在缓存中=True
⏭️ 调试: 跳过已存在记录 - ID=2, 跳过总数=2
🔍 调试: 智能补全检查 - ID=3, 在缓存中=True
⏭️ 调试: 跳过已存在记录 - ID=3, 跳过总数=3
🔍 调试: 智能补全检查 - ID=4, 在缓存中=True
⏭️ 调试: 跳过已存在记录 - ID=4, 跳过总数=4
🔍 调试: 智能补全检查 - ID=5, 在缓存中=True
⏭️ 调试: 跳过已存在记录 - ID=5, 跳过总数=5
🔍 调试: 智能补全检查 - ID=6, 在缓存中=True
🔍 调试: 智能补全检查 - ID=7, 在缓存中=True
🔍 调试: 智能补全检查 - ID=8, 在缓存中=True
🔍 调试: 智能补全检查 - ID=9, 在缓存中=True
🔍 调试: 智能补全检查 - ID=10, 在缓存中=True

============================================================
📊 RELEASES 智能补全处理报告
============================================================

🎯 处理概览:
  • 本次新增记录: 0 条
  • XML文件总记录: 18,333,401 条
  • 跳过已存在记录: 7,935,059 条
  • 实际尝试处理: 10,398,342 条记录
  • 成功率: 0.00%

📈 智能补全统计:
  • 成功插入新记录: 0 条
  • 成功更新记录: 0 条
  • 总成功记录: 0 条

📋 处理结果分类:

⏱️ 性能指标:
  • 处理时长: 2072.83 秒 (34.5 分钟)
  • 平均处理速度: 0 秒/记录
  • 智能补全模式：检查所有记录
  • 未处理任何记录
  • 起始yId: YRD34419593

📁 日志文件:
  • 详细日志: process_output.txt
  • 无错误日志

✅ 数据完整性检查:
  ✅ 所有尝试处理的记录都已成功处理

============================================================

详细输出已保存到: process_output.txt
