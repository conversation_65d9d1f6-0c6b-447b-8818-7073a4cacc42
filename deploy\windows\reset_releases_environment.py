#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Releases进度和日志清理脚本
用于清理releases处理的进度文件和日志文件，为增量插入处理做准备
保留数据库中的现有数据不变
"""

import os
import sys
import glob
from datetime import datetime

# 需要清理的文件和目录配置
FILES_TO_CLEAN = [
    'progress/releases_progress.json',
    'logs/releases_errors.log',
    'logs/releases_process_output.txt',
    'process_output.txt',
    'releases_output.txt',
]

# 需要清理的日志文件模式
LOG_PATTERNS = [
    'logs/*releases*.log',
    'logs/*releases*.txt',
    '*releases*output*.txt',
]

def clean_files_by_pattern(pattern):
    """根据模式清理文件"""
    try:
        import glob
        files = glob.glob(pattern)
        cleaned_count = 0
        for file_path in files:
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"🗑️ 已删除文件: {file_path}")
                cleaned_count += 1
        return cleaned_count
    except Exception as e:
        print(f"❌ 清理文件模式 {pattern} 失败: {e}")
        return 0

def delete_file_if_exists(file_path):
    """删除文件（如果存在）"""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            print(f"🗑️ 已删除文件: {file_path}")
            return True
        else:
            print(f"ℹ️ 文件不存在: {file_path}")
            return True
    except Exception as e:
        print(f"❌ 删除文件失败 {file_path}: {e}")
        return False

def create_directories():
    """创建必要的目录"""
    directories = ['progress', 'logs']
    for directory in directories:
        try:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"📁 创建目录: {directory}")
            else:
                print(f"ℹ️ 目录已存在: {directory}")
        except Exception as e:
            print(f"❌ 创建目录失败 {directory}: {e}")

def verify_environment():
    """验证环境设置"""
    print("\n🔍 验证环境设置...")

    # 检查XML文件
    xml_patterns = ['data/*_releases.xml.gz', '../data/*_releases.xml.gz']
    xml_found = False
    for pattern in xml_patterns:
        import glob
        files = glob.glob(pattern)
        if files:
            file_size = os.path.getsize(files[0]) / (1024 * 1024 * 1024)  # GB
            print(f"✅ 找到XML文件: {files[0]} ({file_size:.2f} GB)")
            xml_found = True
            break

    if not xml_found:
        print("⚠️ 未找到releases XML文件，请确保文件存在")

    # 检查Python脚本
    if os.path.exists('process_releases.py'):
        print("✅ 找到处理脚本: process_releases.py")
    else:
        print("❌ 未找到处理脚本: process_releases.py")

    # 检查必要目录
    directories = ['progress', 'logs']
    for directory in directories:
        if os.path.exists(directory):
            print(f"✅ 目录存在: {directory}")
        else:
            print(f"⚠️ 目录不存在: {directory} (将自动创建)")

    return xml_found

def main():
    """主函数"""
    print("="*60)
    print("🧹 RELEASES 进度和日志清理工具")
    print("="*60)
    print("此工具将清理releases处理的进度文件和日志文件")
    print("✅ 保留数据库中的现有数据不变")
    print("⚠️ 警告: 这将删除现有的进度文件和日志文件")
    print()

    # 用户确认
    response = input("是否继续清理进度和日志文件？(y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("❌ 操作已取消")
        return

    print("\n🚀 开始清理进度和日志文件...")

    try:
        # 1. 清理指定的文件
        print("\n🗑️ 清理指定文件...")
        cleaned_files = 0
        for file_path in FILES_TO_CLEAN:
            if delete_file_if_exists(file_path):
                cleaned_files += 1

        # 2. 清理匹配模式的文件
        print("\n🗑️ 清理匹配模式的文件...")
        pattern_cleaned = 0
        for pattern in LOG_PATTERNS:
            count = clean_files_by_pattern(pattern)
            pattern_cleaned += count

        # 3. 创建必要目录
        print("\n📁 创建必要目录...")
        create_directories()

        # 4. 验证环境
        verify_environment()

        print("\n" + "="*60)
        print("✅ 进度和日志清理完成！")
        print("="*60)
        print("📋 清理摘要:")
        print(f"  • 清理指定文件: {cleaned_files} 个")
        print(f"  • 清理模式匹配文件: {pattern_cleaned} 个")
        print(f"  • 数据库数据: 保持不变")
        print(f"  • 必要目录: 已确保存在")
        print()
        print("🚀 现在可以运行增量插入处理:")
        print("   python3 process_releases.py")
        print()
        print("💡 增量插入特性:")
        print("   • 保留现有数据库记录")
        print("   • 只插入缺失的记录")
        print("   • 自动跳过重复记录")
        print("   • 支持断点续传")
        print()

    except Exception as e:
        print(f"❌ 清理过程中出错: {e}")

if __name__ == "__main__":
    main()
