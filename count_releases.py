#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import gzip
import time
import os
import glob
import sys

def find_xml_file(module_type):
    """
    从 data 目录获取指定模块的XML文件

    Args:
        module_type: 模块类型 ('artists', 'labels', 'masters', 'releases')

    Returns:
        找到的文件路径，如果没找到返回None
    """
    # 尝试多个可能的数据目录路径
    possible_dirs = ['data', 'deploy/windows/data', '../deploy/windows/data']

    for data_dir in possible_dirs:
        if os.path.exists(data_dir):
            pattern = os.path.join(data_dir, f'*_{module_type}.xml.gz')
            found_files = glob.glob(pattern)
            if found_files:
                break
    else:
        found_files = []

    if not found_files:
        print(f"❌ 未找到 {module_type} 模块的XML文件")
        print(f"   搜索路径: {pattern}")
        return None

    # 如果找到多个文件，选择最新的（按文件名排序）
    if len(found_files) > 1:
        found_files.sort()
        selected_file = found_files[-1]
        print(f"🔍 找到多个文件，选择最新的: {selected_file}")
    else:
        selected_file = found_files[0]
        print(f"✅ 检测到文件: {selected_file}")

    return selected_file

def format_number(num):
    """格式化数字显示，添加千位分隔符"""
    return f"{num:,}"

def format_time(seconds):
    """格式化时间显示"""
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}分钟"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}小时"

def get_file_size(file_path):
    """获取文件大小（字节）"""
    return os.path.getsize(file_path)

def count_release_records(xml_file):
    """统计XML文件中的release记录数量"""
    print(f"\n开始统计文件: {xml_file}")
    
    # 获取文件大小信息
    file_size = get_file_size(xml_file)
    file_size_mb = file_size / (1024 * 1024)
    file_size_gb = file_size / (1024 * 1024 * 1024)
    
    print(f"文件大小: {format_number(file_size)} 字节 ({file_size_mb:.1f} MB / {file_size_gb:.2f} GB)")
    print("="*60)
    
    start_time = time.time()
    record_count = 0
    lines_processed = 0
    
    # 进度显示间隔
    progress_interval = 100000  # 每10万条记录显示一次进度
    
    try:
        with gzip.open(xml_file, 'rt', encoding='utf-8') as f:
            for line in f:
                lines_processed += 1
                
                # 检查是否包含release开始标签
                if '<release ' in line:
                    record_count += 1
                    
                    # 显示进度
                    if record_count % progress_interval == 0:
                        elapsed_time = time.time() - start_time
                        records_per_second = record_count / elapsed_time if elapsed_time > 0 else 0
                        
                        print(f"📊 已统计: {format_number(record_count)} 条记录")
                        print(f"   处理速度: {records_per_second:.0f} 记录/秒")
                        print(f"   已用时间: {format_time(elapsed_time)}")
                        print(f"   已处理行数: {format_number(lines_processed)}")
                        print("-" * 40)
                
                # 每处理100万行显示一次行数进度
                if lines_processed % 1000000 == 0:
                    elapsed_time = time.time() - start_time
                    print(f"📄 已处理 {format_number(lines_processed)} 行，用时 {format_time(elapsed_time)}")
    
    except Exception as e:
        print(f"❌ 处理过程中出错: {e}")
        return None
    
    # 计算最终统计结果
    total_time = time.time() - start_time
    
    return {
        'record_count': record_count,
        'lines_processed': lines_processed,
        'processing_time': total_time,
        'file_size': file_size,
        'records_per_second': record_count / total_time if total_time > 0 else 0
    }

def main():
    """主函数"""
    print("🎵 Discogs Release 数据统计工具")
    print("="*60)
    
    # 查找XML文件
    xml_file = find_xml_file('releases')
    if not xml_file:
        print("❌ 错误: 无法找到 releases 模块的XML数据文件")
        print("请确保文件存在于当前目录或 data 目录下，文件名格式: discogs_YYYYMMDD_releases.xml.gz")
        sys.exit(1)
    
    # 执行统计
    result = count_release_records(xml_file)
    
    if result:
        # 输出最终统计结果
        print("\n" + "="*60)
        print("📈 统计结果报告")
        print("="*60)
        print(f"文件路径: {xml_file}")
        print(f"文件大小: {format_number(result['file_size'])} 字节 ({result['file_size']/(1024**3):.2f} GB)")
        print(f"总记录数: {format_number(result['record_count'])} 条")
        print(f"处理行数: {format_number(result['lines_processed'])} 行")
        print(f"处理时间: {format_time(result['processing_time'])}")
        print(f"处理速度: {result['records_per_second']:.0f} 记录/秒")
        print("="*60)
        
        # 验证预期数量
        expected_min = 20000000  # 2000万
        if result['record_count'] >= expected_min:
            print(f"✅ 记录数量符合预期 (>= {format_number(expected_min)} 条)")
        else:
            print(f"⚠️ 记录数量低于预期 (< {format_number(expected_min)} 条)")
        
        print(f"\n🎉 统计完成！共发现 {format_number(result['record_count'])} 条 release 记录")
    else:
        print("❌ 统计失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
