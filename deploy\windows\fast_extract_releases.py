#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
高效提取release gzip数据中最后10条记录
使用优化的读取策略和进度显示
"""

import gzip
import time
import re
import sys
from collections import deque

def extract_release_id(content):
    """从release标签中提取id属性"""
    pattern = r'<release[^>]*id="(\d+)"'
    match = re.search(pattern, content)
    return int(match.group(1)) if match else None

def main():
    xml_file = "data/discogs_20250701_releases.xml.gz"
    
    print("🚀 开始提取最后10条release记录...")
    print(f"📁 文件: {xml_file}")
    print("📊 正在初始化...")
    
    start_time = time.time()
    release_records = deque(maxlen=10)
    buffer = ""
    in_release = False
    total_records = 0
    last_progress_time = start_time
    
    try:
        print("📖 开始读取文件...")
        sys.stdout.flush()
        
        with gzip.open(xml_file, 'rt', encoding='utf-8', bufsize=8192*16) as f:
            for line_num, line in enumerate(f, 1):
                if '<release ' in line:
                    buffer = line
                    in_release = True
                    total_records += 1
                    
                    # 每100万条记录显示进度
                    if total_records % 1000000 == 0:
                        elapsed = time.time() - start_time
                        current_id = None
                        if release_records:
                            current_id = extract_release_id(release_records[-1])
                        
                        progress_msg = f"📊 已处理 {total_records:,} 条记录 | 当前最大ID: {current_id} | 用时: {elapsed/60:.1f}分钟"
                        print(progress_msg)
                        sys.stdout.flush()
                        
                elif '</release>' in line and in_release:
                    buffer += line
                    in_release = False
                    release_records.append(buffer.strip())
                    buffer = ""
                    
                elif in_release:
                    buffer += line
                
                # 每30秒显示时间进度
                current_time = time.time()
                if current_time - last_progress_time >= 30:
                    elapsed = current_time - start_time
                    current_id = None
                    if release_records:
                        current_id = extract_release_id(release_records[-1])
                    
                    time_msg = f"⏱️ 运行时间: {elapsed/60:.1f}分钟 | 已处理: {total_records:,} 条 | 当前最大ID: {current_id}"
                    print(time_msg)
                    sys.stdout.flush()
                    last_progress_time = current_time
        
        # 输出结果
        scan_time = time.time() - start_time
        print(f"\n✅ 扫描完成!")
        print(f"⏱️ 总用时: {scan_time:.2f}秒 ({scan_time/60:.1f}分钟)")
        print(f"📈 总记录数: {total_records:,}")
        print(f"📋 提取记录数: {len(release_records)}")
        
        if not release_records:
            print("❌ 未找到任何记录")
            return
        
        print(f"\n{'='*100}")
        print(f"📋 最后 {len(release_records)} 条Release记录")
        print(f"{'='*100}")
        
        for i, record in enumerate(release_records, 1):
            record_id = extract_release_id(record)
            
            # 提取title
            title_match = re.search(r'<title>([^<]*)</title>', record)
            title = title_match.group(1)[:60] if title_match else "无标题"
            
            print(f"\n--- 记录 {i:2d} | ID: {record_id:>10} | Title: {title} ---")
            
            # 显示记录内容，但限制长度以避免输出过长
            if len(record) > 2000:
                print(record[:1000])
                print(f"\n... [记录内容过长，已截断，完整长度: {len(record)} 字符] ...")
                print(record[-1000:])
            else:
                print(record)
            
            if i < len(release_records):
                print(f"\n{'-'*100}")
        
        print(f"\n{'='*100}")
        print(f"✅ 完成 - 共显示 {len(release_records)} 条记录")
        print(f"🕒 完成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*100}")
        
    except KeyboardInterrupt:
        print(f"\n❌ 用户中断操作")
        elapsed = time.time() - start_time
        print(f"⏱️ 已运行: {elapsed/60:.1f}分钟")
        print(f"📊 已处理: {total_records:,} 条记录")
        if release_records:
            print(f"📋 当前已收集: {len(release_records)} 条记录")
        
    except Exception as e:
        print(f"❌ 处理出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
