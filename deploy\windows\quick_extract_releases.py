#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速提取release gzip数据中最后10条记录
"""

import gzip
import time
import re
from collections import deque

def extract_release_id(content):
    """从release标签中提取id属性"""
    pattern = r'<release[^>]*id="(\d+)"'
    match = re.search(pattern, content)
    return int(match.group(1)) if match else None

def main():
    xml_file = "data/discogs_20250701_releases.xml.gz"
    
    print("🚀 开始提取最后10条release记录...")
    print(f"📁 文件: {xml_file}")
    
    start_time = time.time()
    release_records = deque(maxlen=10)
    buffer = ""
    in_release = False
    total_records = 0
    
    try:
        with gzip.open(xml_file, 'rt', encoding='utf-8') as f:
            for line in f:
                if '<release ' in line:
                    buffer = line
                    in_release = True
                    total_records += 1
                    
                    # 每50万条记录显示进度
                    if total_records % 500000 == 0:
                        elapsed = time.time() - start_time
                        current_id = None
                        if release_records:
                            current_id = extract_release_id(release_records[-1])
                        print(f"📊 已处理 {total_records:,} 条记录 | 当前最大ID: {current_id} | 用时: {elapsed/60:.1f}分钟")
                        
                elif '</release>' in line and in_release:
                    buffer += line
                    in_release = False
                    release_records.append(buffer.strip())
                    buffer = ""
                    
                elif in_release:
                    buffer += line
        
        # 输出结果
        scan_time = time.time() - start_time
        print(f"\n✅ 扫描完成!")
        print(f"⏱️ 用时: {scan_time:.2f}秒 ({scan_time/60:.1f}分钟)")
        print(f"📈 总记录数: {total_records:,}")
        print(f"📋 提取记录数: {len(release_records)}")
        
        print(f"\n{'='*80}")
        print(f"📋 最后 {len(release_records)} 条Release记录")
        print(f"{'='*80}")
        
        for i, record in enumerate(release_records, 1):
            record_id = extract_release_id(record)
            
            # 提取title
            title_match = re.search(r'<title>([^<]*)</title>', record)
            title = title_match.group(1)[:50] if title_match else "无标题"
            
            print(f"\n--- 记录 {i:2d} | ID: {record_id:>10} | Title: {title} ---")
            print(record)
            
            if i < len(release_records):
                print(f"\n{'-'*80}")
        
        print(f"\n{'='*80}")
        print(f"✅ 完成 - 共显示 {len(release_records)} 条记录")
        print(f"{'='*80}")
        
    except Exception as e:
        print(f"❌ 处理出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
