# Artists 模块 Windows 部署环境配置文件
# 请根据实际环境修改以下配置

# 模块标识
MODULE_NAME=artists
MODULE_DISPLAY_NAME=Artists 艺术家

# XML 数据文件路径（支持相对路径和绝对路径）
# 留空表示自动检测文件名（推荐），或指定具体文件名
# XML_FILE=discogs_20250601_artists.xml.gz
XML_FILE=

# MongoDB 连接配置
MONGO_URI=**********************************************************
DB_NAME=music_test

# 处理配置 - Artists 数据量相对较小，可以设置较大的批次
# MAX_RECORDS=50000
MAX_RECORDS=0

# 输出文件路径
OUTPUT_FILE=logs/artists_process_output.txt

# 进度保存配置
PROGRESS_FILE=progress/artists_progress.json
CHECKPOINT_INTERVAL=1000

# 长时间运行配置
AUTO_RESTART=true
MAX_MEMORY_MB=2048
LOG_ROTATION_SIZE_MB=100

# Python 环境配置
PYTHON_ENV=venv
