# Releases 模块 Windows 部署环境配置文件
# 请根据实际环境修改以下配置

# 模块标识
MODULE_NAME=releases
MODULE_DISPLAY_NAME=Releases 发行版

# XML 数据文件路径（支持相对路径和绝对路径）
# 留空表示自动检测文件名（推荐），或指定具体文件名
# XML_FILE=discogs_20250601_releases.xml.gz
XML_FILE=

# MongoDB 连接配置
MONGO_URI=**********************************************************
DB_NAME=music_test

# 处理配置 - Releases 完整重新处理
# MAX_RECORDS=10000  # 设置为0表示处理全部数据
MAX_RECORDS=0

# 起始ID配置 - 从第一条记录开始处理
START_RELEASE_ID=1

# 输出文件路径
OUTPUT_FILE=logs/releases_process_output.txt

# 进度保存配置
PROGRESS_FILE=progress/releases_progress.json
CHECKPOINT_INTERVAL=1000

# 长时间运行配置 - 关闭自动重启，支持手动断点续传
AUTO_RESTART=false
MAX_MEMORY_MB=4096
LOG_ROTATION_SIZE_MB=50
RESTART_INTERVAL_HOURS=12

# Python 环境配置
PYTHON_ENV=venv

# 特殊配置 - 针对超大数据集
ENABLE_MEMORY_MONITORING=true
ENABLE_DISK_MONITORING=true
MIN_FREE_DISK_GB=10
