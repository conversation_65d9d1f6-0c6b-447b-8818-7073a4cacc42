#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
分析XML文件结构，检查release记录的解析问题
"""

import gzip
import re
import time

def extract_release_id(content):
    """从release标签中提取id属性"""
    pattern = r'<release[^>]*id="(\d+)"'
    match = re.search(pattern, content)
    return int(match.group(1)) if match else None

def analyze_xml_structure(xml_file, sample_size=1000):
    """分析XML文件结构"""
    print("=" * 80)
    print("🔍 XML文件结构分析")
    print("=" * 80)
    
    total_records = 0
    sample_records = []
    parsing_issues = []
    
    start_time = time.time()
    
    try:
        with gzip.open(xml_file, 'rt', encoding='utf-8') as f:
            buffer = ""
            in_release = False
            line_number = 0
            
            for line in f:
                line_number += 1
                
                if '<release ' in line:
                    buffer = line
                    in_release = True
                    total_records += 1
                elif '</release>' in line and in_release:
                    buffer += line
                    in_release = False
                    
                    # 提取release ID
                    release_id = extract_release_id(buffer)
                    
                    if release_id is None:
                        parsing_issues.append({
                            'line': line_number,
                            'issue': 'Cannot extract release ID',
                            'content_preview': buffer[:200]
                        })
                    
                    # 收集样本
                    if len(sample_records) < sample_size:
                        sample_records.append({
                            'id': release_id,
                            'line': line_number,
                            'content_length': len(buffer),
                            'has_title': '<title>' in buffer,
                            'has_artists': '<artists>' in buffer,
                            'has_labels': '<labels>' in buffer
                        })
                    
                    # 清空缓冲区
                    buffer = ""
                    
                    # 显示进度
                    if total_records % 100000 == 0:
                        elapsed = time.time() - start_time
                        print(f"已分析 {total_records:,} 条记录，用时 {elapsed:.1f} 秒")
                        
                elif in_release:
                    buffer += line
                
                # 限制分析数量以节省时间
                if total_records >= 500000:  # 分析前50万条记录
                    break
    
    except Exception as e:
        print(f"❌ 分析过程中出错: {e}")
        return
    
    # 输出分析结果
    print(f"\n📊 分析结果:")
    print(f"  • 总记录数: {total_records:,}")
    print(f"  • 解析问题数: {len(parsing_issues)}")
    print(f"  • 样本记录数: {len(sample_records)}")
    
    # ID范围分析
    if sample_records:
        valid_ids = [r['id'] for r in sample_records if r['id'] is not None]
        if valid_ids:
            print(f"\n📈 ID范围 (前{len(valid_ids)}条记录):")
            print(f"  • 最小ID: {min(valid_ids)}")
            print(f"  • 最大ID: {max(valid_ids)}")
            print(f"  • ID跨度: {max(valid_ids) - min(valid_ids)}")
    
    # 字段完整性分析
    if sample_records:
        has_title = sum(1 for r in sample_records if r['has_title'])
        has_artists = sum(1 for r in sample_records if r['has_artists'])
        has_labels = sum(1 for r in sample_records if r['has_labels'])
        
        print(f"\n🔍 字段完整性 (样本分析):")
        print(f"  • 有title字段: {has_title}/{len(sample_records)} ({has_title/len(sample_records)*100:.1f}%)")
        print(f"  • 有artists字段: {has_artists}/{len(sample_records)} ({has_artists/len(sample_records)*100:.1f}%)")
        print(f"  • 有labels字段: {has_labels}/{len(sample_records)} ({has_labels/len(sample_records)*100:.1f}%)")
    
    # 解析问题详情
    if parsing_issues:
        print(f"\n❌ 解析问题详情 (前10个):")
        for i, issue in enumerate(parsing_issues[:10], 1):
            print(f"  {i}. 行 {issue['line']}: {issue['issue']}")
            print(f"     内容预览: {issue['content_preview'][:100]}...")
    
    # 样本记录展示
    print(f"\n📋 样本记录 (前10个):")
    for i, record in enumerate(sample_records[:10], 1):
        print(f"  {i:2d}. ID: {record['id']:>8} | 行: {record['line']:>6} | 长度: {record['content_length']:>4} | "
              f"title: {'✓' if record['has_title'] else '✗'} | "
              f"artists: {'✓' if record['has_artists'] else '✗'} | "
              f"labels: {'✓' if record['has_labels'] else '✗'}")
    
    print("\n" + "=" * 80)

def main():
    xml_file = "data/discogs_20250701_releases.xml.gz"
    analyze_xml_structure(xml_file)

if __name__ == "__main__":
    main()
