#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Releases数据完整性验证脚本
用于验证XML文件和数据库中的记录数量，分析数据完整性
"""

import os
import sys
import gzip
import re
from pymongo import MongoClient
from datetime import datetime

# 数据库配置
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')

def find_xml_file():
    """查找XML文件"""
    import glob
    patterns = ['data/*_releases.xml.gz', '../data/*_releases.xml.gz']
    
    for pattern in patterns:
        files = glob.glob(pattern)
        if files:
            return files[0]
    return None

def connect_to_mongodb():
    """连接到MongoDB"""
    try:
        client = MongoClient(MONGO_URI)
        client.admin.command('ping')
        return client, client[DB_NAME]
    except Exception as e:
        print(f"❌ MongoDB连接失败: {e}")
        return None, None

def extract_release_id(content):
    """从release标签中提取id属性"""
    pattern = r'<release[^>]*id="(\d+)"'
    match = re.search(pattern, content)
    return int(match.group(1)) if match else None

def count_xml_records(xml_file, sample_size=1000):
    """统计XML文件中的记录数量和ID范围"""
    print(f"🔍 分析XML文件: {xml_file}")
    
    total_records = 0
    min_id = float('inf')
    max_id = 0
    sample_ids = []
    
    try:
        with gzip.open(xml_file, 'rt', encoding='utf-8') as f:
            buffer = ""
            in_release = False
            
            for line_num, line in enumerate(f, 1):
                if '<release ' in line:
                    buffer = line
                    in_release = True
                    total_records += 1
                elif '</release>' in line and in_release:
                    buffer += line
                    in_release = False
                    
                    # 提取ID
                    release_id = extract_release_id(buffer)
                    if release_id:
                        min_id = min(min_id, release_id)
                        max_id = max(max_id, release_id)
                        
                        # 收集样本ID
                        if len(sample_ids) < sample_size:
                            sample_ids.append(release_id)
                    
                    buffer = ""
                elif in_release:
                    buffer += line
                
                # 进度显示
                if line_num % 1000000 == 0:
                    print(f"  已扫描 {line_num:,} 行，找到 {total_records:,} 条记录...")
    
    except Exception as e:
        print(f"❌ 读取XML文件失败: {e}")
        return None
    
    return {
        'total_records': total_records,
        'min_id': min_id if min_id != float('inf') else None,
        'max_id': max_id if max_id > 0 else None,
        'sample_ids': sample_ids[:100]  # 只保留前100个样本
    }

def analyze_database(db):
    """分析数据库中的记录"""
    print("🔍 分析数据库记录...")
    
    try:
        collection = db['release_new']
        
        # 总记录数
        total_count = collection.count_documents({})
        
        if total_count == 0:
            return {
                'total_count': 0,
                'min_id': None,
                'max_id': None,
                'sample_ids': []
            }
        
        # ID范围分析
        pipeline_min = [
            {'$addFields': {'id_numeric': {'$toInt': '$id'}}},
            {'$sort': {'id_numeric': 1}},
            {'$limit': 1},
            {'$project': {'id': 1}}
        ]
        
        pipeline_max = [
            {'$addFields': {'id_numeric': {'$toInt': '$id'}}},
            {'$sort': {'id_numeric': -1}},
            {'$limit': 1},
            {'$project': {'id': 1}}
        ]
        
        min_result = list(collection.aggregate(pipeline_min))
        max_result = list(collection.aggregate(pipeline_max))
        
        min_id = int(min_result[0]['id']) if min_result else None
        max_id = int(max_result[0]['id']) if max_result else None
        
        # 样本ID
        sample_pipeline = [
            {'$addFields': {'id_numeric': {'$toInt': '$id'}}},
            {'$sort': {'id_numeric': 1}},
            {'$limit': 100},
            {'$project': {'id': 1}}
        ]
        
        sample_result = list(collection.aggregate(sample_pipeline))
        sample_ids = [int(doc['id']) for doc in sample_result]
        
        return {
            'total_count': total_count,
            'min_id': min_id,
            'max_id': max_id,
            'sample_ids': sample_ids
        }
        
    except Exception as e:
        print(f"❌ 分析数据库失败: {e}")
        return None

def find_missing_ranges(xml_sample, db_sample):
    """查找缺失的ID范围"""
    xml_set = set(xml_sample)
    db_set = set(db_sample)
    
    missing_in_db = xml_set - db_set
    extra_in_db = db_set - xml_set
    
    return sorted(missing_in_db), sorted(extra_in_db)

def main():
    """主函数"""
    print("="*60)
    print("🔍 RELEASES 数据完整性验证工具")
    print("="*60)
    
    # 1. 查找XML文件
    xml_file = find_xml_file()
    if not xml_file:
        print("❌ 未找到releases XML文件")
        return
    
    # 2. 连接数据库
    client, db = connect_to_mongodb()
    if not client:
        return
    
    try:
        print("\n📊 开始数据完整性分析...")
        
        # 3. 分析XML文件
        print("\n" + "="*40)
        xml_stats = count_xml_records(xml_file)
        if not xml_stats:
            return
        
        # 4. 分析数据库
        print("\n" + "="*40)
        db_stats = analyze_database(db)
        if db_stats is None:
            return
        
        # 5. 生成报告
        print("\n" + "="*60)
        print("📋 数据完整性分析报告")
        print("="*60)
        
        print(f"\n📁 XML文件分析:")
        print(f"  • 文件路径: {xml_file}")
        print(f"  • 总记录数: {xml_stats['total_records']:,}")
        print(f"  • ID范围: {xml_stats['min_id']:,} - {xml_stats['max_id']:,}")
        
        print(f"\n💾 数据库分析:")
        print(f"  • 集合名称: release_new")
        print(f"  • 总记录数: {db_stats['total_count']:,}")
        if db_stats['total_count'] > 0:
            print(f"  • ID范围: {db_stats['min_id']:,} - {db_stats['max_id']:,}")
        else:
            print(f"  • 数据库为空")
        
        print(f"\n📊 完整性对比:")
        missing_count = xml_stats['total_records'] - db_stats['total_count']
        completion_rate = (db_stats['total_count'] / xml_stats['total_records'] * 100) if xml_stats['total_records'] > 0 else 0
        
        print(f"  • 缺失记录数: {missing_count:,}")
        print(f"  • 完成率: {completion_rate:.2f}%")
        
        if missing_count > 0:
            print(f"  • ⚠️ 数据不完整，缺失 {missing_count:,} 条记录")
        else:
            print(f"  • ✅ 数据完整")
        
        # 6. 样本分析
        if len(xml_stats['sample_ids']) > 0 and len(db_stats['sample_ids']) > 0:
            missing_sample, extra_sample = find_missing_ranges(
                xml_stats['sample_ids'][:50], 
                db_stats['sample_ids'][:50]
            )
            
            if missing_sample:
                print(f"\n🔍 样本缺失ID分析 (前10个):")
                for i, missing_id in enumerate(missing_sample[:10]):
                    print(f"  • 缺失ID: {missing_id}")
        
        print(f"\n💡 建议:")
        if missing_count > 0:
            print(f"  • 建议运行完整的重新处理")
            print(f"  • 使用 reset_releases_environment.py 清理环境")
            print(f"  • 然后运行 process_releases.py 重新处理")
        else:
            print(f"  • 数据完整性良好，无需重新处理")
        
        print("\n" + "="*60)
        
    except Exception as e:
        print(f"❌ 验证过程中出错: {e}")
    finally:
        if client:
            client.close()

if __name__ == "__main__":
    main()
