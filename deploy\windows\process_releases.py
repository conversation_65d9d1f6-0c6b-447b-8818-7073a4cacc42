#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import gzip
import time
import re
import os
import glob
import sys
from pymongo import MongoClient
from datetime import datetime, timezone

try:
    # 尝试相对导入（当作为模块导入时）
    from .enums import Permissions, DeleteStatus, Source
except ImportError:
    # 直接导入（当直接运行脚本时）
    from enums import Permissions, DeleteStatus, Source

from enum import Enum

class ErrorType(Enum):
    """错误类型分类"""
    FATAL_XML_PARSE = "fatal_xml_parse"  # 致命XML解析错误
    RECOVERABLE_XML_PARSE = "recoverable_xml_parse"  # 可恢复XML解析错误
    DATABASE_CONNECTION = "database_connection"  # 数据库连接错误
    DATABASE_OPERATION = "database_operation"  # 数据库操作错误
    VALIDATION_FAILURE = "validation_failure"  # 数据验证失败
    DUPLICATE_KEY = "duplicate_key"  # 重复键错误
    UNKNOWN = "unknown"  # 未知错误

class ProcessingResult(Enum):
    """处理结果类型"""
    SUCCESS_INSERT = "success_insert"  # 成功插入
    SUCCESS_UPDATE = "success_update"  # 成功更新
    SKIPPED_DUPLICATE = "skipped_duplicate"  # 跳过重复记录
    SKIPPED_FATAL_ERROR = "skipped_fatal_error"  # 跳过致命错误
    SKIPPED_VALIDATION = "skipped_validation"  # 跳过验证失败
    RETRY_EXHAUSTED = "retry_exhausted"  # 重试次数耗尽

class ErrorTracker:
    """错误追踪和统计类"""
    def __init__(self):
        self.error_counts = {error_type: 0 for error_type in ErrorType}
        self.result_counts = {result_type: 0 for result_type in ProcessingResult}
        self.detailed_errors = []
        self.skipped_records = []

    def record_error(self, error_type: ErrorType, record_id=None, details="", line_number=0):
        """记录错误"""
        self.error_counts[error_type] += 1
        self.detailed_errors.append({
            'type': error_type.value,
            'record_id': record_id,
            'details': details,
            'line_number': line_number,
            'timestamp': datetime.now().isoformat()
        })

    def record_result(self, result_type: ProcessingResult, record_id=None):
        """记录处理结果"""
        self.result_counts[result_type] += 1
        if result_type in [ProcessingResult.SKIPPED_DUPLICATE, ProcessingResult.SKIPPED_FATAL_ERROR,
                          ProcessingResult.SKIPPED_VALIDATION, ProcessingResult.RETRY_EXHAUSTED]:
            self.skipped_records.append({
                'record_id': record_id,
                'reason': result_type.value,
                'timestamp': datetime.now().isoformat()
            })

    def get_summary(self):
        """获取错误统计摘要"""
        total_errors = sum(self.error_counts.values())
        total_skipped = len(self.skipped_records)

        return {
            'total_errors': total_errors,
            'total_skipped': total_skipped,
            'error_breakdown': dict(self.error_counts),
            'result_breakdown': dict(self.result_counts),
            'skipped_records': self.skipped_records
        }

# 全局错误追踪器
error_tracker = ErrorTracker()

def find_xml_file(module_type):
    """
    从 data 目录获取指定模块的XML文件

    Args:
        module_type: 模块类型 ('artists', 'labels', 'masters', 'releases')

    Returns:
        找到的文件路径，如果没找到返回None
    """
    # 尝试多个可能的数据目录路径
    possible_dirs = ['data', 'deploy/windows/data', '../deploy/windows/data']

    for data_dir in possible_dirs:
        if os.path.exists(data_dir):
            pattern = os.path.join(data_dir, f'*_{module_type}.xml.gz')
            found_files = glob.glob(pattern)
            if found_files:
                break
    else:
        found_files = []

    if not found_files:
        print(f"❌ 未找到 {module_type} 模块的XML文件")
        print(f"   搜索路径: {pattern}")
        return None

    # 如果找到多个文件，选择最新的（按文件名排序）
    if len(found_files) > 1:
        found_files.sort()
        selected_file = found_files[-1]
        print(f"🔍 找到多个文件，选择最新的: {selected_file}")
    else:
        selected_file = found_files[0]
        print(f"✅ 检测到文件: {selected_file}")

    return selected_file

# 配置参数
print("🔍 正在查找XML文件...")
XML_FILE = find_xml_file('releases')
print(f"📁 找到XML文件: {XML_FILE}")

if not XML_FILE:
    print("❌ 错误: 无法找到 releases 模块的XML数据文件")
    print("请确保文件存在于当前目录或 data 目录下，文件名格式: discogs_YYYYMMDD_releases.xml.gz")
    sys.exit(1)

MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')
MAX_RECORDS = int(os.getenv('MAX_RECORDS', '0'))  # 最大处理记录数，设置为0表示处理全部数据

# 输出文件路径
OUTPUT_FILE = os.getenv('OUTPUT_FILE', 'process_output.txt')

# 错误追踪文件路径
ERROR_FILE = os.getenv('ERROR_FILE', 'logs/releases_errors.log')

# 进度文件路径
PROGRESS_FILE = os.getenv('PROGRESS_FILE', 'progress/releases_progress.json')

# 起始release ID配置
START_RELEASE_ID = int(os.getenv('START_RELEASE_ID', '1'))  # 从指定的release ID开始处理

# 调试模式配置
DEBUG_MODE = os.getenv('DEBUG_MODE', 'true').lower() == 'true'  # 是否启用详细日志

# 断点续传配置
CHECKPOINT_INTERVAL = int(os.getenv('CHECKPOINT_INTERVAL', '1000'))  # 每处理多少条记录保存一次进度

# 智能补全模式配置
SMART_COMPLETION_MODE = os.getenv('SMART_COMPLETION_MODE', 'true').lower() == 'true'  # 启用智能补全模式
BATCH_CHECK_SIZE = int(os.getenv('BATCH_CHECK_SIZE', '10000'))  # 批量检查ID存在性的批次大小
ONLINE_ID_CHECK_MODE = os.getenv('ONLINE_ID_CHECK_MODE', 'false').lower() == 'true'  # 在线ID检查模式（不构建完整缓存）

def load_progress():
    """从进度文件加载上次的处理位置，支持智能补全模式"""
    import json

    if not os.path.exists(PROGRESS_FILE):
        if SMART_COMPLETION_MODE:
            print(f"📖 没有进度文件，智能补全模式：将检查所有记录的存在性")
            return {'mode': 'smart_completion', 'resume_from_id': None}
        else:
            print(f"📖 没有进度文件，将从数据库最大ID+1开始处理")
            return None

    try:
        with open(PROGRESS_FILE, 'r', encoding='utf-8') as f:
            progress_data = json.load(f)

            # 检查是否为智能补全模式
            if progress_data.get('smart_completion_mode', False):
                resume_from_id = progress_data.get('resume_from_release_id', None)
                processed_count = progress_data.get('processed_count', 0)
                skipped_existing = progress_data.get('skipped_existing', 0)
                print(f"📖 智能补全模式：已处理 {processed_count} 条记录，跳过已存在 {skipped_existing} 条")
                return {
                    'mode': 'smart_completion',
                    'resume_from_id': resume_from_id,
                    'processed_count': processed_count,
                    'skipped_existing': skipped_existing
                }
            else:
                # 兼容旧版本进度文件
                resume_from_id = progress_data.get('resume_from_release_id', None)
                last_release_id = progress_data.get('last_release_id', 0)
                processed_count = progress_data.get('processed_count', 0)

                if resume_from_id and not SMART_COMPLETION_MODE:
                    print(f"📖 从进度文件读取：上次处理到release ID {last_release_id}，下次从ID {resume_from_id}开始，已处理 {processed_count} 条记录")
                    return resume_from_id
                else:
                    print(f"📖 切换到智能补全模式，将检查所有记录的存在性")
                    return {'mode': 'smart_completion', 'resume_from_id': None}
    except Exception as e:
        print(f"⚠️ 读取进度文件失败: {e}")
        if SMART_COMPLETION_MODE:
            print(f"📖 启用智能补全模式")
            return {'mode': 'smart_completion', 'resume_from_id': None}
        else:
            print(f"📖 将从数据库最大ID+1开始处理")
            return None

# 确保输出文件不存在
if os.path.exists(OUTPUT_FILE):
    os.remove(OUTPUT_FILE)

def write_output(message, print_to_console=True):
    """将消息写入输出文件，并可选择是否打印到控制台"""
    with open(OUTPUT_FILE, 'a', encoding='utf-8') as f:
        f.write(message + '\n')

    if print_to_console:
        print(message)

def write_error(message, xml_content="", line_number=0):
    """将错误信息写入错误追踪文件"""
    # 确保错误日志目录存在
    error_dir = os.path.dirname(ERROR_FILE)
    if error_dir and not os.path.exists(error_dir):
        os.makedirs(error_dir)

    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    with open(ERROR_FILE, 'a', encoding='utf-8') as f:
        f.write(f"[{timestamp}] Line {line_number}: {message}\n")
        if xml_content:
            f.write(f"XML Content Preview: {xml_content[:200]}...\n")
        f.write("-" * 80 + "\n")

def save_progress(processed_count, total_records_found, last_release_id, skipped_existing=0, smart_completion_mode=False):
    """保存处理进度到文件，支持智能补全模式"""
    import json
    import os

    # 确保progress目录存在
    progress_dir = os.path.dirname(PROGRESS_FILE)
    if progress_dir and not os.path.exists(progress_dir):
        os.makedirs(progress_dir)

    if smart_completion_mode:
        progress_data = {
            'smart_completion_mode': True,
            'processed_count': processed_count,
            'skipped_existing': skipped_existing,
            'total_records_found': total_records_found,
            'last_update': datetime.now().isoformat(),
            'last_release_id': last_release_id,
            'resume_from_release_id': last_release_id + 1 if last_release_id else None
        }
    else:
        progress_data = {
            'smart_completion_mode': False,
            'processed_count': processed_count,
            'total_records_found': total_records_found,
            'last_update': datetime.now().isoformat(),
            'last_release_id': last_release_id,
            'resume_from_release_id': last_release_id + 1  # 下次从这个ID开始处理
        }

    try:
        with open(PROGRESS_FILE, 'w', encoding='utf-8') as f:
            json.dump(progress_data, f, indent=2, ensure_ascii=False)
    except Exception as e:
        write_output(f"保存进度失败: {e}", False)

def connect_to_mongodb():
    """连接到MongoDB并返回数据库对象"""
    try:
        client = MongoClient(MONGO_URI)
        # 测试连接
        client.admin.command('ping')
        return client, client[DB_NAME]
    except Exception as e:
        write_output(f"❌ MongoDB连接失败: {e}")
        raise

def verify_database_operation(operation_result, operation_type, record_id, collection=None):
    """验证数据库操作是否成功，准确区分操作类型"""
    try:
        if operation_type == "skip_duplicate" or operation_type == "skip_existing":
            # 重复记录跳过，这是成功的情况，但不计入processed_count
            if DEBUG_MODE:
                write_output(f"⏭️ 记录已存在，跳过 - ID: {record_id}", False)
            return "skipped"
        elif operation_type == "insert_new":
            # 智能补全模式：真正插入了新记录
            if operation_result and operation_result.acknowledged and operation_result.inserted_id:
                if DEBUG_MODE:
                    write_output(f"✅ 新记录插入成功 - ID: {record_id}", False)
                return "inserted"
            else:
                write_output(f"❌ 新记录插入失败 - ID: {record_id}", False)
                return "failed"
        elif operation_type == "insert_failed":
            # 插入操作失败
            write_output(f"❌ 记录插入失败 - ID: {record_id}", False)
            return "failed"
        elif operation_type == "update_one":
            # 传统模式：update操作
            if operation_result.acknowledged:
                if operation_result.upserted_id:
                    # 实际是插入了新记录
                    if DEBUG_MODE:
                        write_output(f"✅ 通过upsert插入新记录 - ID: {record_id}", False)
                    return "inserted"
                elif operation_result.modified_count > 0:
                    # 更新了现有记录
                    if DEBUG_MODE:
                        write_output(f"📝 更新现有记录 - ID: {record_id}", False)
                    return "updated"
                else:
                    # 记录已存在且无需更新
                    if DEBUG_MODE:
                        write_output(f"⏭️ 记录已存在且无需更新 - ID: {record_id}", False)
                    return "skipped"
            else:
                write_output(f"❌ 数据库操作未确认 - ID: {record_id}", False)
                return "failed"
        elif operation_type == "insert_one":
            # 传统模式：insert操作
            if operation_result.acknowledged and operation_result.inserted_id:
                if DEBUG_MODE:
                    write_output(f"✅ 记录插入成功 - ID: {record_id}", False)
                return "inserted"
            else:
                write_output(f"❌ 数据插入失败 - ID: {record_id}", False)
                return "failed"
        else:
            write_output(f"❌ 未知操作类型: {operation_type} - ID: {record_id}", False)
            return "failed"
    except Exception as e:
        write_output(f"❌ 验证数据库操作时出错 - ID: {record_id}, 错误: {e}", False)
        return "failed"

def get_max_id_from_collection(collection):
    """获取集合中的最大ID值，用于断点续传"""
    try:
        # 尝试将ID转换为数字进行排序，获取最大值
        pipeline = [
            {
                '$addFields': {
                    'id_numeric': {
                        '$toInt': '$id'
                    }
                }
            },
            {
                '$sort': {
                    'id_numeric': -1
                }
            },
            {
                '$limit': 1
            },
            {
                '$project': {
                    'id': 1
                }
            }
        ]

        result = list(collection.aggregate(pipeline))
        if result:
            max_id = result[0]['id']
            write_output(f"📊 数据库中最大release ID: {max_id}", False)
            return int(max_id) if max_id else 0
        else:
            write_output(f"📊 数据库为空，从release ID 1开始处理", False)
            return 0
    except Exception as e:
        write_output(f"⚠️ 获取最大ID失败: {e}，从release ID 1开始处理", False)
        return 0

def build_existing_ids_cache_optimized(collection, page_size=10000, max_timeout=300):
    """优化的ID缓存构建函数，使用分页查询和超时机制"""
    import time
    from pymongo import ASCENDING

    try:
        print(f"🔍 正在构建已存在ID缓存（优化版本）...")
        write_output(f"🔍 正在构建已存在ID缓存（优化版本）...", True)

        start_time = time.time()
        existing_ids = set()
        count = 0
        type_errors = 0
        page_num = 0

        # 首先获取总记录数
        try:
            total_records = collection.count_documents({})
            print(f"📊 数据库中共有 {total_records:,} 条记录，开始分页查询...")
            write_output(f"📊 数据库中共有 {total_records:,} 条记录，开始分页查询...", True)
        except Exception as count_error:
            print(f"⚠️ 无法获取总记录数: {count_error}，继续分页查询...")
            total_records = 0

        # 分页查询所有ID
        while True:
            page_start_time = time.time()

            # 检查总超时
            if time.time() - start_time > max_timeout:
                error_msg = f"❌ ID缓存构建超时 ({max_timeout}秒)，已缓存 {count:,} 个ID"
                print(error_msg)
                write_output(error_msg, True)
                break

            try:
                # 分页查询
                skip_count = page_num * page_size
                cursor = collection.find({}, {'id': 1}).skip(skip_count).limit(page_size).sort('_id', ASCENDING)

                page_records = list(cursor)
                if not page_records:
                    # 没有更多记录，结束查询
                    break

                page_num += 1
                page_count = 0

                # 处理当前页的记录
                for doc in page_records:
                    try:
                        id_value = doc.get('id')
                        if id_value is not None:
                            int_id = int(id_value)
                            existing_ids.add(int_id)
                            count += 1
                            page_count += 1

                            # 调试前几个ID
                            if DEBUG_MODE and count <= 5:
                                write_output(f"🔍 调试: ID缓存 - 原始值='{id_value}' (类型:{type(id_value)}), 转换后={int_id} (类型:{type(int_id)})", True)
                        else:
                            type_errors += 1

                    except (ValueError, TypeError) as convert_error:
                        type_errors += 1
                        if DEBUG_MODE and type_errors <= 5:
                            write_output(f"⚠️ 调试: ID类型转换失败 - 原始值='{doc.get('id')}', 错误={convert_error}", True)
                        continue

                # 显示分页进度
                page_time = time.time() - page_start_time
                elapsed_time = time.time() - start_time

                if total_records > 0:
                    progress_pct = (count / total_records) * 100
                    progress_msg = f"📊 第{page_num}页: 处理 {page_count} 条记录，累计 {count:,}/{total_records:,} ({progress_pct:.1f}%)，用时 {elapsed_time:.1f}s"
                else:
                    progress_msg = f"📊 第{page_num}页: 处理 {page_count} 条记录，累计 {count:,} 条，用时 {elapsed_time:.1f}s"

                print(progress_msg)
                write_output(progress_msg, True)

                # 内存使用检查
                if count % 500000 == 0 and count > 0:
                    import psutil
                    try:
                        memory_usage = psutil.Process().memory_info().rss / 1024 / 1024  # MB
                        memory_msg = f"� 内存使用: {memory_usage:.1f} MB，已缓存 {count:,} 个ID"
                        print(memory_msg)
                        write_output(memory_msg, True)
                    except:
                        pass  # 忽略内存检查错误

            except Exception as page_error:
                error_msg = f"❌ 第{page_num}页查询失败: {page_error}"
                print(error_msg)
                write_output(error_msg, True)

                # 如果是超时错误，尝试继续
                if "timeout" in str(page_error).lower():
                    print("⏳ 检测到超时，等待5秒后继续...")
                    time.sleep(5)
                    continue
                else:
                    # 其他错误，停止构建
                    break

        # 构建完成统计
        total_time = time.time() - start_time
        success_msg = f"✅ ID缓存构建完成，共缓存 {len(existing_ids):,} 个已存在的ID，用时 {total_time:.1f} 秒"
        print(success_msg)
        write_output(success_msg, True)

        if type_errors > 0:
            write_output(f"⚠️ ID类型转换错误: {type_errors} 个", True)

        # 调试：显示缓存统计
        if DEBUG_MODE and existing_ids:
            sample_ids = sorted(list(existing_ids))[:10]
            write_output(f"🔍 调试: 缓存中的前10个ID: {sample_ids}", True)

            if len(existing_ids) > 10:
                sample_ids_end = sorted(list(existing_ids))[-5:]
                write_output(f"🔍 调试: 缓存中的最后5个ID: {sample_ids_end}", True)

        return existing_ids

    except Exception as e:
        error_msg = f"❌ 构建ID缓存失败: {e}"
        print(error_msg)
        write_output(error_msg, True)
        return set()

def analyze_missing_ids(collection, max_id_range=35000000):
    """分析数据库中缺失的ID，生成精确的缺失ID列表"""
    try:
        print(f"🔍 正在分析数据库中的缺失ID...")
        write_output(f"🔍 正在分析数据库中的缺失ID...", True)

        # 获取数据库中所有存在的ID
        print(f"📊 查询数据库中所有存在的ID...")
        cursor = collection.find({}, {'id': 1}).sort('id', 1)
        existing_ids = set()

        count = 0
        for doc in cursor:
            existing_ids.add(int(doc['id']))
            count += 1

            if count % 500000 == 0:
                print(f"📊 已读取 {count:,} 个现有ID...")

        print(f"✅ 数据库中共有 {len(existing_ids):,} 个现有ID")
        write_output(f"✅ 数据库中共有 {len(existing_ids):,} 个现有ID", True)

        # 分析ID范围
        if existing_ids:
            min_id = min(existing_ids)
            max_id = max(existing_ids)
            print(f"📈 ID范围: {min_id:,} - {max_id:,}")
            write_output(f"📈 ID范围: {min_id:,} - {max_id:,}", True)

            # 生成缺失ID列表（限制在合理范围内）
            analysis_max_id = min(max_id, max_id_range)
            print(f"🔍 分析范围: 1 - {analysis_max_id:,}")

            missing_ids = set()
            for i in range(1, analysis_max_id + 1):
                if i not in existing_ids:
                    missing_ids.add(i)

            print(f"❌ 发现缺失ID: {len(missing_ids):,} 个")
            write_output(f"❌ 发现缺失ID: {len(missing_ids):,} 个", True)

            # 显示缺失ID的分布统计
            ranges = [
                (1, 10000),
                (10001, 50000),
                (50001, 100000),
                (100001, 500000),
                (500001, 1000000),
                (1000001, 5000000),
                (5000001, analysis_max_id)
            ]

            print(f"📊 缺失ID分布统计:")
            write_output(f"📊 缺失ID分布统计:", True)

            for start, end in ranges:
                if end > analysis_max_id:
                    end = analysis_max_id
                if start > analysis_max_id:
                    break

                range_missing = sum(1 for mid in missing_ids if start <= mid <= end)
                range_total = end - start + 1
                missing_pct = (range_missing / range_total) * 100

                stats_msg = f"  • ID {start:>8,} - {end:>8,}: 缺失 {range_missing:>8,}/{range_total:>8,} ({missing_pct:>5.1f}%)"
                print(stats_msg)
                write_output(stats_msg, True)

            return missing_ids
        else:
            print(f"❌ 数据库为空")
            return set(range(1, max_id_range + 1))

    except Exception as e:
        error_msg = f"❌ 分析缺失ID失败: {e}"
        print(error_msg)
        write_output(error_msg, True)
        return set()

def build_existing_ids_cache(collection, batch_size=50000):
    """构建已存在ID的缓存集合，智能选择构建策略"""
    try:
        # 首先尝试获取记录总数来决定策略
        total_records = collection.count_documents({})

        if total_records > 5000000:  # 超过500万条记录使用优化版本
            print(f"📊 检测到大量记录 ({total_records:,} 条)，使用优化的分页查询策略")
            return build_existing_ids_cache_optimized(collection)
        else:
            print(f"📊 记录数量适中 ({total_records:,} 条)，使用标准查询策略")
            # 使用原有的简化版本
            existing_ids = set()
            cursor = collection.find({}, {'id': 1}).batch_size(batch_size)
            count = 0

            for doc in cursor:
                try:
                    id_value = doc.get('id')
                    if id_value is not None:
                        existing_ids.add(int(id_value))
                        count += 1

                        if count % 100000 == 0:
                            print(f"📊 已缓存 {count:,} 个ID...")
                except:
                    continue

            print(f"✅ ID缓存构建完成，共缓存 {len(existing_ids):,} 个已存在的ID")
            return existing_ids

    except Exception as e:
        print(f"❌ 构建ID缓存失败: {e}")
        return set()

def smart_completion_database_operation(collection, document):
    """智能补全模式专用的数据库操作，确保不重复插入"""
    try:
        record_id = document['id']

        # 第一步：实时检查记录是否真的不存在（使用整数类型ID）
        existing_record = collection.find_one({'id': int(record_id)}, {'_id': 1})

        if existing_record:
            # 记录已存在，这是缓存不准确的情况
            if DEBUG_MODE:
                write_output(f"⚠️ 智能补全: 记录 ID={record_id} 实际已存在，跳过插入", True)
            return None, "skip_existing"

        # 第二步：记录确实不存在，执行插入操作
        try:
            if DEBUG_MODE:
                write_output(f"🔍 智能补全: 确认记录不存在，执行插入 ID={record_id}", True)

            result = collection.insert_one(document)

            if DEBUG_MODE:
                write_output(f"🔍 智能补全: 插入操作完成 - acknowledged={result.acknowledged}, inserted_id={result.inserted_id}", True)

            # 第三步：验证插入是否真的成功
            if result.acknowledged and result.inserted_id:
                # 再次检查记录是否真的被插入（使用整数类型ID）
                verification = collection.find_one({'id': int(record_id)}, {'_id': 1})
                if verification:
                    return result, "insert_new"
                else:
                    write_output(f"❌ 智能补全: 插入后验证失败 - ID: {record_id}", True)
                    return None, "insert_failed"
            else:
                write_output(f"❌ 智能补全: 插入操作未确认 - ID: {record_id}", True)
                return None, "insert_failed"

        except Exception as insert_error:
            # 插入失败，检查是否是重复键错误
            if "duplicate key" in str(insert_error).lower() or "11000" in str(insert_error):
                write_output(f"⚠️ 智能补全: ID {record_id} 插入时发现重复键（并发插入），跳过", True)
                return None, "skip_duplicate"
            else:
                # 其他类型的错误，重新抛出
                write_output(f"❌ 智能补全: 插入操作异常 - ID: {record_id}, 错误: {insert_error}", True)
                raise insert_error

    except Exception as e:
        write_output(f"❌ 智能补全数据库操作异常 - ID: {document.get('id', 'Unknown')}, 错误: {e}", True)
        raise

def check_single_id_exists(collection, record_id):
    """在线检查单个ID是否存在于数据库中"""
    try:
        result = collection.find_one({'id': int(record_id)}, {'_id': 1})
        return result is not None
    except Exception as e:
        write_output(f"❌ 检查ID {record_id} 存在性失败: {e}", False)
        return False

def check_ids_exist_in_batch(collection, id_list):
    """批量检查ID是否存在于数据库中"""
    try:
        if not id_list:
            return set()

        # 确保ID列表中的所有ID都是整数类型
        int_ids = [int(id_val) for id_val in id_list]

        # 批量查询存在的ID（使用整数类型）
        existing_docs = collection.find(
            {'id': {'$in': int_ids}},
            {'id': 1}
        )

        # 返回存在的ID集合（已经是整数类型）
        existing_ids = {doc['id'] for doc in existing_docs}
        return existing_ids

    except Exception as e:
        write_output(f"❌ 批量检查ID存在性失败: {e}", False)
        return set()

def smart_database_operation(collection, document, max_id_in_db):
    """智能选择数据库操作策略：优先insert_one，失败后fallback到update_one"""
    try:
        current_id = int(document['id'])

        # 如果当前ID大于数据库中的最大ID，尝试直接插入
        if current_id > max_id_in_db:
            try:
                result = collection.insert_one(document)
                return result, "insert_one"
            except Exception as insert_error:
                # 插入失败（可能是重复键），降级为update操作
                if "duplicate key" in str(insert_error).lower() or "11000" in str(insert_error):
                    write_output(f"🔄 ID {current_id} 插入失败，降级为更新操作", False)
                    result = collection.update_one(
                        {'id': document['id']},
                        {'$set': document},
                        upsert=True
                    )
                    return result, "update_one"
                else:
                    raise insert_error
        else:
            # 当前ID小于等于最大ID，直接使用update操作
            result = collection.update_one(
                {'id': document['id']},
                {'$set': document},
                upsert=True
            )
            return result, "update_one"

    except Exception as e:
        write_output(f"❌ 数据库操作异常 - ID: {document.get('id', 'Unknown')}, 错误: {e}", False)
        raise

def ensure_database_indexes(collection):
    """确保数据库索引存在以优化插入性能"""
    try:
        # 检查id字段的索引
        indexes = collection.list_indexes()
        id_index_exists = False

        for index in indexes:
            if 'id' in index.get('key', {}):
                id_index_exists = True
                break

        if not id_index_exists:
            print("🔧 创建id字段唯一索引...")
            collection.create_index('id', unique=True)
            print("✅ id字段索引创建成功")
        else:
            print("✅ id字段索引已存在")

    except Exception as e:
        print(f"⚠️ 索引检查/创建失败: {e}")

def direct_insert_operation(collection, document):
    """直接插入操作：不检查存在性，直接插入记录"""
    try:
        record_id = document['id']

        # 直接执行插入操作
        result = collection.insert_one(document)

        if result.acknowledged and result.inserted_id:
            error_tracker.record_result(ProcessingResult.SUCCESS_INSERT, record_id)
            return result, "insert_success"
        else:
            error_tracker.record_error(ErrorType.DATABASE_OPERATION, record_id, "插入操作未确认")
            write_output(f"❌ 插入操作失败 - ID: {record_id}", False)
            return None, "insert_failed"

    except Exception as e:
        # 插入过程中出现错误
        error_str = str(e).lower()
        if "duplicate key" in error_str or "11000" in error_str:
            # 重复键错误，这在完全重新插入模式下不应该发生
            error_tracker.record_error(ErrorType.DUPLICATE_KEY, record_id, f"意外的重复键错误: {e}")
            write_output(f"⚠️ 意外的重复键错误 - ID: {record_id}, 错误: {e}", False)
            return None, "duplicate_error"
        else:
            # 其他插入错误
            error_tracker.record_error(ErrorType.DATABASE_OPERATION, record_id, f"插入错误: {e}")
            write_output(f"❌ 插入异常 - ID: {record_id}, 错误: {e}", False)
            raise e





def safe_database_operation(collection, operation_type, *args, **kwargs):
    """安全的数据库操作，包含重试机制"""
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        try:
            if operation_type == "update_one":
                result = collection.update_one(*args, **kwargs)
            elif operation_type == "insert_one":
                result = collection.insert_one(*args, **kwargs)
            else:
                raise ValueError(f"不支持的操作类型: {operation_type}")

            return result
        except Exception as e:
            retry_count += 1
            if retry_count >= max_retries:
                write_output(f"❌ 数据库操作失败，已重试{max_retries}次: {e}", False)
                raise
            else:
                write_output(f"⚠️ 数据库操作失败，正在重试({retry_count}/{max_retries}): {e}", False)
                time.sleep(1)  # 等待1秒后重试

def safe_direct_insert_operation(collection, document):
    """安全的直接插入操作，包含重试机制"""
    max_retries = 3
    retry_count = 0
    record_id = document.get('id', 'Unknown')

    while retry_count < max_retries:
        try:
            result, operation_used = direct_insert_operation(collection, document)
            return result, operation_used
        except Exception as e:
            retry_count += 1
            error_str = str(e).lower()

            # 分类处理不同类型的错误
            if "duplicate key" in error_str or "11000" in error_str:
                # 重复键错误 - 在完全重新插入模式下不应该发生
                error_tracker.record_error(ErrorType.DUPLICATE_KEY, record_id, f"意外的重复键错误: {e}")
                write_output(f"❌ 意外的重复键错误 - ID: {record_id}: {e}", False)
                error_tracker.record_result(ProcessingResult.RETRY_EXHAUSTED, record_id)
                raise
            elif "timeout" in error_str or "connection" in error_str:
                # 连接或超时错误 - 可恢复错误
                error_tracker.record_error(ErrorType.DATABASE_CONNECTION, record_id, f"连接错误: {e}")
                if retry_count >= max_retries:
                    error_tracker.record_result(ProcessingResult.RETRY_EXHAUSTED, record_id)
                    write_output(f"❌ 数据库连接问题，重试耗尽 - ID: {record_id}: {e}", False)
                    raise
                else:
                    wait_time = min(retry_count * 2, 10)  # 指数退避，最大10秒
                    write_output(f"⚠️ 数据库连接问题，等待{wait_time}秒后重试({retry_count}/{max_retries}) - ID: {record_id}: {e}", False)
                    time.sleep(wait_time)
            else:
                # 其他数据库操作错误
                error_tracker.record_error(ErrorType.DATABASE_OPERATION, record_id, f"数据库操作错误: {e}")
                if retry_count >= max_retries:
                    error_tracker.record_result(ProcessingResult.RETRY_EXHAUSTED, record_id)
                    write_output(f"❌ 数据库操作失败，已重试{max_retries}次 - ID: {record_id}: {e}", False)
                    raise
                else:
                    write_output(f"⚠️ 数据库操作失败，正在重试({retry_count}/{max_retries}) - ID: {record_id}: {e}", False)
                    time.sleep(1)

def extract_field(content, field_name):
    """从XML内容中提取指定字段的值"""
    # 支持带属性的标签，如 <master_id attr="value">content</master_id>
    pattern = f'<{field_name}[^>]*>(.*?)</{field_name}>'
    match = re.search(pattern, content, re.DOTALL)
    return match.group(1).strip() if match else None

def extract_attribute(content, tag_name, attribute_name):
    """从XML内容中提取指定标签的属性值"""
    # 匹配标签及其属性，支持多种属性顺序
    pattern = f'<{tag_name}[^>]*{attribute_name}="([^"]*)"[^>]*>'
    match = re.search(pattern, content)
    return match.group(1).strip() if match else None

def extract_release_id(content):
    """从release标签中提取id属性"""
    pattern = r'<release[^>]*id="(\d+)"'
    match = re.search(pattern, content)
    return int(match.group(1)) if match else None

def extract_artists(content):
    """提取主要艺术家字段 (artists)"""
    artists = []

    # 提取主要艺术家 (artists)
    artists_match = re.search(r'<artists>(.*?)</artists>', content, re.DOTALL)
    if artists_match:
        artists_content = artists_match.group(1)
        artist_pattern = r'<artist>(.*?)</artist>'
        artist_matches = re.findall(artist_pattern, artists_content, re.DOTALL)

        for artist_content in artist_matches:
            artist_id = extract_field(artist_content, 'id')
            # 支持 <name> 和 <n> 两种标签格式
            name = extract_field(artist_content, 'name') or extract_field(artist_content, 'n')
            anv = extract_field(artist_content, 'anv')
            role = extract_field(artist_content, 'role') or "Primary"

            if artist_id and name:
                artist_doc = {
                    'artist_id': int(artist_id),
                    'name': name,
                    'role': role
                }
                if anv:
                    artist_doc['anv'] = anv
                artists.append(artist_doc)

    return artists

def extract_extra_artists(content):
    """提取额外艺术家字段 (extraartists)"""
    extra_artists = []

    # 提取额外艺术家 (extraartists)
    extra_artists_match = re.search(r'<extraartists>(.*?)</extraartists>', content, re.DOTALL)
    if extra_artists_match:
        extra_artists_content = extra_artists_match.group(1)
        artist_pattern = r'<artist>(.*?)</artist>'
        artist_matches = re.findall(artist_pattern, extra_artists_content, re.DOTALL)

        for artist_content in artist_matches:
            artist_id = extract_field(artist_content, 'id')
            # 支持 <name> 和 <n> 两种标签格式
            name = extract_field(artist_content, 'name') or extract_field(artist_content, 'n')
            anv = extract_field(artist_content, 'anv')
            role = extract_field(artist_content, 'role') or "Unknown"

            if artist_id and name:
                artist_doc = {
                    'artist_id': int(artist_id),
                    'name': name,
                    'role': role
                }
                if anv:
                    artist_doc['anv'] = anv
                extra_artists.append(artist_doc)

    return extra_artists



def extract_labels(content):
    """提取labels字段"""
    labels = []
    labels_match = re.search(r'<labels>(.*?)</labels>', content, re.DOTALL)
    if not labels_match:
        return labels
    
    labels_content = labels_match.group(1)
    label_pattern = r'<label[^>]*name="([^"]*)"[^>]*catno="([^"]*)"[^>]*id="([^"]*)"[^>]*/?>'
    label_matches = re.findall(label_pattern, labels_content)
    
    for name, catno, label_id in label_matches:
        if name:
            labels.append({
                'name': name,
                'catno': catno,
                'id': label_id
            })
    
    return labels

def extract_companies(content):
    """提取companies字段"""
    companies = []
    companies_match = re.search(r'<companies>(.*?)</companies>', content, re.DOTALL)
    if not companies_match:
        return companies

    companies_content = companies_match.group(1)
    company_pattern = r'<company>(.*?)</company>'
    company_matches = re.findall(company_pattern, companies_content, re.DOTALL)

    for company_content in company_matches:
        company_id = extract_field(company_content, 'id')
        # 支持 <name> 和 <n> 两种标签格式
        name = extract_field(company_content, 'name') or extract_field(company_content, 'n')
        entity_type = extract_field(company_content, 'entity_type')
        entity_type_name = extract_field(company_content, 'entity_type_name')
        resource_url = extract_field(company_content, 'resource_url')

        if name:  # 只要有名称就添加
            company_doc = {
                'name': name
            }

            # 添加可选字段
            if company_id:
                company_doc['id'] = company_id
            if entity_type:
                company_doc['entity_type'] = entity_type
            if entity_type_name:
                company_doc['entity_type_name'] = entity_type_name
            if resource_url:
                company_doc['resource_url'] = resource_url

            companies.append(company_doc)

    return companies

def extract_formats(content):
    """提取formats字段"""
    formats = []
    formats_match = re.search(r'<formats>(.*?)</formats>', content, re.DOTALL)
    if not formats_match:
        return formats
    
    formats_content = formats_match.group(1)
    format_pattern = r'<format[^>]*name="([^"]*)"[^>]*qty="([^"]*)"[^>]*text="([^"]*)"[^>]*>(.*?)</format>'
    format_matches = re.findall(format_pattern, formats_content, re.DOTALL)
    
    for name, qty, text, format_inner in format_matches:
        format_doc = {
            'name': name,
            'qty': qty,
            'text': text,
            'descriptions': []
        }
        
        # 提取descriptions
        desc_pattern = r'<description>([^<]*)</description>'
        descriptions = re.findall(desc_pattern, format_inner)
        format_doc['descriptions'] = descriptions
        
        formats.append(format_doc)
    
    return formats

def extract_list_field(content, field_name, item_name):
    """提取列表字段（如genres, styles）"""
    items = []
    field_match = re.search(f'<{field_name}>(.*?)</{field_name}>', content, re.DOTALL)
    if not field_match:
        return items
    
    field_content = field_match.group(1)
    item_pattern = f'<{item_name}>([^<]*)</{item_name}>'
    items = re.findall(item_pattern, field_content)
    
    return items

def extract_identifiers(content):
    """提取identifiers字段"""
    identifiers = []
    identifiers_match = re.search(r'<identifiers>(.*?)</identifiers>', content, re.DOTALL)
    if not identifiers_match:
        return identifiers
    
    identifiers_content = identifiers_match.group(1)
    identifier_pattern = r'<identifier[^>]*type="([^"]*)"[^>]*value="([^"]*)"[^>]*description="([^"]*)"[^>]*/?>'
    identifier_matches = re.findall(identifier_pattern, identifiers_content)
    
    for type_val, value, description in identifier_matches:
        identifiers.append({
            'type': type_val,
            'value': value,
            'description': description
        })
    
    return identifiers

def extract_tracklist(content):
    """提取tracklist字段"""
    tracklist = []
    tracklist_match = re.search(r'<tracklist>(.*?)</tracklist>', content, re.DOTALL)
    if not tracklist_match:
        return tracklist
    
    tracklist_content = tracklist_match.group(1)
    track_pattern = r'<track>(.*?)</track>'
    track_matches = re.findall(track_pattern, tracklist_content, re.DOTALL)
    
    for track_content in track_matches:
        position = extract_field(track_content, 'position')
        title = extract_field(track_content, 'title')
        duration = extract_field(track_content, 'duration')
        
        if position and title:
            track_doc = {
                'position': position,
                'title': title
            }
            if duration:
                track_doc['duration'] = duration
            tracklist.append(track_doc)
    
    return tracklist

def get_release_table_by_id(db, release_id):
    """从release表中获取images字段"""
    try:
        # 使用整数类型进行查询
        release_doc = db.release.find_one({'id': int(release_id)})
        if release_doc and 'images' in release_doc:
            return release_doc
            # //['images']
        return []
    except Exception as e:
        write_output(f"获取images失败 (release_id: {release_id}): {e}", False)
        return []


def process_release_content(buffer, yid_counter, db, line_number=0):
    """处理单个release标签的内容，增强错误处理和分类"""
    record_id = None
    try:
        # 提取release ID
        release_id = extract_release_id(buffer)
        record_id = release_id  # 用于错误追踪

        if not release_id:
            error_tracker.record_error(ErrorType.FATAL_XML_PARSE, None, "无法提取release ID", line_number)
            write_error(f"无法提取release ID", buffer, line_number)
            return None

        # 验证ID是否为有效格式
        if not str(release_id).strip():
            error_tracker.record_error(ErrorType.FATAL_XML_PARSE, release_id, "release ID为空", line_number)
            write_error(f"release ID为空", buffer, line_number)
            return None

        # 生成yId
        y_id = f"YRD{yid_counter}"

        # 记录详细的处理信息
        write_output(f"🔍 处理记录: Line {line_number}, ID={release_id}, y_id={y_id}", False)

        # 提取 discogs_status 并记录调试信息
        discogs_status = extract_attribute(buffer, 'release', 'status') or 'unknown'

        # 调试日志：记录 status 提取结果（仅在前几条记录中记录）
        if yid_counter <= 5:
            write_output(f"调试: release_id={release_id}, discogs_status='{discogs_status}'", False)

        old_release_doc = get_release_table_by_id(db, release_id)
        # 安全地获取字段，处理返回值可能是空列表的情况
        if isinstance(old_release_doc, dict):
            images = old_release_doc.get('images', [])
            identifiers = old_release_doc.get('identifiers', [])
            notes = old_release_doc.get('notes', [])
            year = old_release_doc.get('year', None)
        else:
            images = []
            identifiers = []
            notes = []
            year = None

        # 提取其他字段，并记录提取失败的情况
        title = extract_field(buffer, 'title')
        if not title:
            # title缺失不是致命错误，记录为可恢复错误
            error_tracker.record_error(ErrorType.RECOVERABLE_XML_PARSE, release_id, "无法提取title字段", line_number)
            write_error(f"无法提取title字段 (ID: {release_id})", buffer, line_number)
            title = ""  # 使用空字符串作为默认值

        # 创建release文档
        release_doc = {
            'y_id': y_id,
            'id': release_id,
            'title': title,

            # —— ARTISTS ——
            'artists': extract_artists(buffer),
            'extra_artists': extract_extra_artists(buffer),

            # —— LABELS & COMPANIES ——
            'labels': extract_labels(buffer),
            'companies': extract_companies(buffer),
            'country': extract_field(buffer, 'country'),

            # —— FORMATS / GENRES / STYLES ——
            'formats': extract_formats(buffer),
            'genres': extract_list_field(buffer, 'genres', 'genre'),
            'styles': extract_list_field(buffer, 'styles', 'style'),

            # —— IDENTIFIERS & IMAGES ——
            'identifiers': identifiers,
            'images': images,
            'notes': notes,
            'year': year,

            # —— TRACKLIST ——
            'tracklist': extract_tracklist(buffer),

            # —— 其余字段 ——
            'master_id': extract_field(buffer, 'master_id'),
            'images_permissions': Permissions.ALL_VISIBLE.value,
            'permissions': Permissions.ALL_VISIBLE.value,

            'source': Source.DISCOGS.value,
            'discogs_status': discogs_status,

            'created_at': datetime.now(timezone.utc),
            'updated_at': datetime.now(timezone.utc),

            'delete_status': DeleteStatus.NOT_DELETED.value,  # 逻辑删除状态，默认未删除
            'deleted_at': None  # 为软删除功能准备
        }

        # 转换master_id为整数
        if release_doc['master_id']:
            try:
                release_doc['master_id'] = int(release_doc['master_id'])
            except ValueError:
                release_doc['master_id'] = None

        return release_doc

    except Exception as e:
        # 根据异常类型进行分类处理
        error_str = str(e).lower()
        if "timeout" in error_str or "connection" in error_str:
            error_tracker.record_error(ErrorType.DATABASE_CONNECTION, record_id, f"数据库连接错误: {e}", line_number)
        elif "parse" in error_str or "xml" in error_str:
            error_tracker.record_error(ErrorType.FATAL_XML_PARSE, record_id, f"XML解析错误: {e}", line_number)
        else:
            error_tracker.record_error(ErrorType.UNKNOWN, record_id, f"未知错误: {e}", line_number)

        write_error(f"处理release内容时出错: {e}", buffer, line_number)
        return None

def process_releases():
    """处理XML文件中的release记录"""
    print("🚀 开始处理releases...")
    start_time = time.time()

    # 连接MongoDB
    print("📡 正在连接MongoDB...")
    client, db = connect_to_mongodb()
    print("✅ MongoDB连接成功")

    # 确保release_new集合存在
    if 'release_new' not in db.list_collection_names():
        db.create_collection('release_new')

    # 获取集合
    release_new_collection = db['release_new']

    # 智能补全模式或传统断点续传模式
    progress_info = load_progress()
    max_id_in_db = get_max_id_from_collection(release_new_collection)

    # 初始化模式相关变量
    smart_completion_mode = False
    existing_ids_cache = set()
    missing_ids_set = set()  # 新增：缺失ID集合
    resume_from_id = None
    skipped_existing_count = 0
    online_id_check_mode = ONLINE_ID_CHECK_MODE  # 本地变量，可以修改

    if isinstance(progress_info, dict) and progress_info.get('mode') == 'smart_completion':
        smart_completion_mode = True
        skipped_existing_count = progress_info.get('skipped_existing', 0)
        print(f"🔄 智能补全模式：基于缺失ID列表进行精确补全")
        print(f"📊 已跳过存在记录: {skipped_existing_count:,} 条")

        # 使用新的缺失ID分析策略
        print(f"� 使用精确缺失ID分析模式")
        missing_ids_set = analyze_missing_ids(release_new_collection)

        if missing_ids_set:
            print(f"✅ 缺失ID分析完成，共发现 {len(missing_ids_set):,} 个缺失ID")
            write_output(f"✅ 缺失ID分析完成，共发现 {len(missing_ids_set):,} 个缺失ID", True)

            # 显示前20个缺失ID作为示例
            sample_missing = sorted(list(missing_ids_set))[:20]
            print(f"📋 前20个缺失ID示例: {sample_missing}")
            write_output(f"📋 前20个缺失ID示例: {sample_missing}", True)
        else:
            print(f"⚠️ 未发现缺失ID或分析失败")
            write_output(f"⚠️ 未发现缺失ID或分析失败", True)

    elif progress_info:
        resume_from_id = progress_info
        print(f"🔄 传统断点续传模式：从release ID {resume_from_id} 开始处理")
    else:
        resume_from_id = max_id_in_db + 1 if max_id_in_db > 0 else 1
        print(f"🔄 传统断点续传模式：从数据库最大ID+1开始，从release ID {resume_from_id} 开始处理")

    print(f"📊 数据库中已有记录的最大ID: {max_id_in_db}")
    print(f"🚀 使用智能插入策略，确保数据完整性")
    print(f"🔍 智能补全模式：{'启用' if smart_completion_mode else '关闭'}")
    print(f"🐛 调试模式：{'启用' if DEBUG_MODE else '关闭'}")

    # 确保数据库索引存在以优化插入性能
    print("\n🔧 检查数据库索引...")
    ensure_database_indexes(release_new_collection)

    print(f"✅ 环境准备完成，开始断点续传处理")



    processed_count = 0
    total_records_found = 0
    skipped_count = 0  # 跳过的记录数（传统模式：ID小于resume_from_id，智能模式：已存在的记录）
    yid_counter = max_id_in_db + 1  # 从数据库最大ID+1开始计数
    last_processed_release_id = max_id_in_db  # 从数据库最大ID开始跟踪

    # 统计变量
    successful_inserts = 0      # 成功插入的记录数
    successful_updates = 0      # 成功更新的记录数
    parsing_errors = 0          # XML解析错误计数
    database_errors = 0         # 数据库错误计数

    # 智能补全模式专用统计
    if smart_completion_mode:
        skipped_count = skipped_existing_count  # 从进度文件恢复已跳过的数量

    # 全局进度监控变量
    last_progress_time = time.time()
    progress_interval = 30  # 每30秒显示一次进度
    heartbeat_interval = 10  # 每10秒显示一次心跳

    # 重置全局错误追踪器
    global error_tracker
    error_tracker = ErrorTracker()

    try:
        write_output(f"开始处理XML文件: {XML_FILE}")

        # 打开gzip文件并逐行读取
        with gzip.open(XML_FILE, 'rt', encoding='utf-8') as f:
            buffer = ""
            in_release = False

            for line in f:
                # 全局进度监控 - 心跳机制
                current_time = time.time()
                if current_time - last_progress_time >= progress_interval:
                    progress_msg = (
                        f"📊 全局进度: 已扫描 {total_records_found:,} 条记录 | "
                        f"已处理 {processed_count:,} 条 | "
                        f"跳过 {skipped_count:,} 条 | "
                        f"用时 {(current_time - start_time)/60:.1f} 分钟"
                    )
                    print(progress_msg)
                    write_output(progress_msg, True)
                    last_progress_time = current_time

                if '<release ' in line:
                    buffer = line
                    in_release = True
                    total_records_found += 1

                    # 每扫描10万条记录显示进度
                    if total_records_found % 100000 == 0:
                        scan_msg = f"📖 已扫描 {total_records_found:,} 条记录..."
                        print(scan_msg)
                        write_output(scan_msg, True)

                elif '</release>' in line and in_release:
                    buffer += line
                    in_release = False

                    # 提取当前release ID（用于统计和进度跟踪）
                    current_release_id = extract_release_id(buffer)

                    # 智能补全模式：基于缺失ID列表进行精确筛选
                    if smart_completion_mode and current_release_id:
                        # 确保ID类型一致性
                        try:
                            int_release_id = int(current_release_id)

                            # 检查当前ID是否在缺失ID列表中
                            if int_release_id not in missing_ids_set:
                                # 当前ID不在缺失列表中，跳过处理
                                skipped_count += 1

                                # 调试前几个跳过的记录
                                if DEBUG_MODE and skipped_count <= 10:
                                    write_output(f"⏭️ 调试: 精确筛选跳过非缺失记录 - ID={int_release_id}, 跳过总数={skipped_count}", True)

                                # 每跳过10万条记录显示进度
                                if skipped_count % 100000 == 0:
                                    skip_msg = f"⏭️ 精确筛选已跳过 {skipped_count:,} 条非缺失记录..."
                                    print(skip_msg)
                                    write_output(skip_msg, True)

                                continue
                            else:
                                # 当前ID在缺失列表中，需要处理
                                if DEBUG_MODE and processed_count < 5:
                                    write_output(f"🎯 调试: 发现缺失记录，准备处理 - ID={int_release_id}", True)
                        except (ValueError, TypeError) as id_error:
                            parsing_errors += 1
                            write_output(f"⚠️ ID类型转换失败，跳过记录 - 原始ID='{current_release_id}', 错误={id_error}", True)
                            continue
                        except Exception as unexpected_error:
                            parsing_errors += 1
                            write_output(f"❌ 智能补全检查时发生意外错误 - ID='{current_release_id}', 错误={unexpected_error}", True)
                            continue

                    # 传统断点续传模式：跳过已处理的记录
                    elif not smart_completion_mode and current_release_id and resume_from_id and current_release_id < resume_from_id:
                        skipped_count += 1
                        continue

                    # 处理release内容
                    release_doc = process_release_content(buffer, yid_counter, db, total_records_found)
                    if release_doc:
                        # 根据模式选择数据库操作策略
                        try:
                            if DEBUG_MODE and processed_count < 5:
                                write_output(f"🔍 调试: 准备插入记录 ID={release_doc['id']}, y_id={release_doc['y_id']}", True)

                            # 显示找到的第一个需要插入的记录
                            if processed_count == 0:
                                first_insert_msg = f"🎯 找到第一个需要插入的记录: ID={release_doc['id']}, y_id={release_doc['y_id']}"
                                print(first_insert_msg)
                                write_output(first_insert_msg, True)

                            # 智能补全模式使用专用的数据库操作函数
                            if smart_completion_mode:
                                result, operation_used = smart_completion_database_operation(
                                    release_new_collection,
                                    release_doc
                                )
                            else:
                                # 传统模式使用原有的智能数据库操作
                                result, operation_used = smart_database_operation(
                                    release_new_collection,
                                    release_doc,
                                    max_id_in_db
                                )

                            if DEBUG_MODE and processed_count < 5:
                                write_output(f"🔍 调试: 数据库操作完成 - mode={'智能补全' if smart_completion_mode else '传统'}, operation={operation_used}, result={result}", True)

                            # 验证数据库操作结果
                            verification_result = verify_database_operation(result, operation_used, release_doc['id'])

                            if verification_result == "inserted":
                                # 真正插入了新记录
                                successful_inserts += 1
                                processed_count += 1
                                yid_counter += 1
                                last_processed_release_id = release_doc['id']

                                if DEBUG_MODE and processed_count <= 5:
                                    write_output(f"✅ 调试: 新记录插入成功 - ID: {release_doc['id']}, processed_count: {processed_count}", True)

                            elif verification_result == "updated":
                                # 更新了现有记录（传统模式）
                                successful_updates += 1
                                processed_count += 1
                                yid_counter += 1
                                last_processed_release_id = release_doc['id']

                                if DEBUG_MODE and processed_count <= 5:
                                    write_output(f"✅ 调试: 记录更新成功 - ID: {release_doc['id']}, processed_count: {processed_count}", True)

                            elif verification_result == "skipped":
                                # 记录已存在，跳过但不计入错误
                                if DEBUG_MODE and skipped_count <= 5:
                                    write_output(f"⏭️ 调试: 记录已存在，跳过 - ID: {release_doc['id']}", True)
                                continue

                            else:  # verification_result == "failed"
                                # 操作失败
                                database_errors += 1
                                write_output(f"❌ 数据库操作失败 - ID: {release_doc['id']}", True)
                                if DEBUG_MODE:
                                    write_output(f"🔍 调试: 操作失败详情 - operation={operation_used}, verification={verification_result}", True)
                                continue

                            # 每10条记录输出日志（匹配artist模块格式）
                            if processed_count % 10 == 0:
                                title_short = release_doc['title'][:50] if release_doc['title'] else "无标题"
                                log_message = (f"处理记录 {processed_count}: y_id={release_doc['y_id']}, "
                                          f"id={release_doc['id']}, title={title_short}...")
                                # 只写入文件，不打印到控制台
                                write_output(log_message, False)
                                # 打印到控制台
                                print(f"已处理 {processed_count} 条记录...")

                        except Exception as e:
                            # 数据库操作异常
                            database_errors += 1
                            write_output(f"❌ 数据库操作异常 - ID: {release_doc.get('id', 'Unknown')}, 错误: {e}", True)

                            # 如果数据库错误过多，可能需要停止处理
                            if database_errors > 1000:
                                error_msg = f"❌ 数据库错误过多 ({database_errors} 个)，可能存在系统性问题"
                                print(error_msg)
                                write_output(error_msg, True)
                                # 但不停止处理，继续尝试

                            continue  # 跳过这条记录，不增加processed_count
                    else:
                        # XML解析失败
                        parsing_errors += 1
                        if DEBUG_MODE and parsing_errors <= 5:
                            write_output(f"⚠️ 调试: XML解析失败 - 记录 {total_records_found}", True)

                        # 定期保存进度（只有在数据成功插入后才保存）
                        if processed_count % CHECKPOINT_INTERVAL == 0:
                            if smart_completion_mode:
                                save_progress(processed_count, total_records_found, last_processed_release_id,
                                            skipped_count, smart_completion_mode)
                            else:
                                save_progress(processed_count, total_records_found, last_processed_release_id)

                            current_time = time.time()
                            elapsed_time = current_time - start_time

                            if smart_completion_mode:
                                checkpoint_msg = (
                                    f"💾 检查点保存: 已处理 {processed_count:,} 条记录 | "
                                    f"跳过已存在 {skipped_count:,} 条 | "
                                    f"用时: {elapsed_time/60:.1f} 分钟 | "
                                    f"最后release ID: {last_processed_release_id}"
                                )
                            else:
                                checkpoint_msg = (
                                    f"💾 检查点保存: 已处理 {processed_count:,} 条记录 | "
                                    f"用时: {elapsed_time/60:.1f} 分钟 | "
                                    f"最后release ID: {last_processed_release_id}"
                                )
                            write_output(checkpoint_msg, True)  # 同时输出到控制台和文件

                        # 达到最大处理记录数时退出（MAX_RECORDS=0表示处理全部数据）
                        if MAX_RECORDS > 0 and processed_count >= MAX_RECORDS:
                            write_output(f"已达到最大处理记录数限制 ({MAX_RECORDS})，停止处理")
                            break

                    # 清空缓冲区
                    buffer = ""
                elif in_release:
                    buffer += line

                # 如果已达到最大记录数，跳出外层循环（MAX_RECORDS=0表示处理全部数据）
                if MAX_RECORDS > 0 and processed_count >= MAX_RECORDS:
                    break

    except Exception as e:
        error_msg = f"处理过程中出错: {e}"
        write_output(error_msg)
    finally:
        # 计算处理时间
        processing_time = time.time() - start_time

        # 获取详细错误统计
        error_summary = error_tracker.get_summary()

        # 计算统计数据
        total_attempted = total_records_found - skipped_count  # 实际尝试处理的记录数
        success_rate = (processed_count / total_attempted * 100) if total_attempted > 0 else 0
        total_failed = parsing_errors + database_errors
        total_successful = successful_inserts + successful_updates

        # 输出处理结果统计
        if smart_completion_mode:
            stats = [
                "\n" + "="*60,
                "📊 RELEASES 智能补全处理报告",
                "="*60,
                "",
                "🎯 处理概览:",
                f"  • 本次新增记录: {processed_count:,} 条",
                f"  • XML文件总记录: {total_records_found:,} 条",
                f"  • 跳过已存在记录: {skipped_count:,} 条",
                f"  • 实际尝试处理: {total_attempted:,} 条记录",
                f"  • 成功率: {success_rate:.2f}%",
                "",
                "📈 智能补全统计:",
                f"  • 成功插入新记录: {successful_inserts:,} 条",
                f"  • 成功更新记录: {successful_updates:,} 条",
                f"  • 总成功记录: {total_successful:,} 条",
            ]
        else:
            stats = [
                "\n" + "="*60,
                "📊 RELEASES 断点续传处理报告",
                "="*60,
                "",
                "🎯 处理概览:",
                f"  • 本次处理记录: {processed_count:,} 条",
                f"  • XML文件总记录: {total_records_found:,} 条",
                f"  • 跳过记录数: {skipped_count:,} 条 (ID < {resume_from_id})" if resume_from_id else f"  • 跳过记录数: {skipped_count:,} 条",
                f"  • 实际尝试处理: {total_attempted:,} 条记录",
                f"  • 成功率: {success_rate:.2f}%",
                "",
                "📈 断点续传统计:",
                f"  • 成功插入记录: {successful_inserts:,} 条",
                f"  • 成功更新记录: {successful_updates:,} 条",
                f"  • 总成功记录: {total_successful:,} 条",
            "",
            "❌ 失败统计详情:",
            f"  • XML解析错误: {parsing_errors:,} 条",
            f"  • 数据库操作错误: {database_errors:,} 条",
            f"  • 总失败记录: {total_failed:,} 条",
            "",
            "🔍 错误类型分析:",
        ]

        # 添加详细错误分析
        for error_type, count in error_summary['error_breakdown'].items():
            if count > 0:
                stats.append(f"  • {error_type}: {count:,} 次")

        stats.extend([
            "",
            "📋 处理结果分类:",
        ])

        # 添加处理结果分析
        for result_type, count in error_summary['result_breakdown'].items():
            if count > 0:
                stats.append(f"  • {result_type}: {count:,} 次")

        stats.extend([
            "",
            "⏱️ 性能指标:",
            f"  • 处理时长: {processing_time:.2f} 秒 ({processing_time/60:.1f} 分钟)",
            (f"  • 平均处理速度: {processing_time/processed_count:.4f} 秒/记录"
             if processed_count > 0 else "  • 平均处理速度: 0 秒/记录"),
            f"  • 断点续传起始ID: {resume_from_id:,}" if resume_from_id else "  • 智能补全模式：检查所有记录",
            f"  • 最后处理ID: {last_processed_release_id:,}" if processed_count > 0 else f"  • 未处理任何记录",
            f"  • 最后生成yId: YRD{yid_counter-1}" if processed_count > 0 else f"  • 起始yId: YRD{max_id_in_db + 1}",
            "",
            "📁 日志文件:",
            f"  • 详细日志: {OUTPUT_FILE}",
            f"  • 错误日志: {ERROR_FILE}" if error_summary['total_errors'] > 0 else "  • 无错误日志",
            "",
            "🚨 数据完整性警告:" if total_failed > 0 else "✅ 数据完整性检查:",
        ])

        if total_failed > 0:
            missing_percentage = (total_failed / total_attempted * 100) if total_attempted > 0 else 0
            stats.extend([
                f"  ⚠️  有 {total_failed:,} 条记录未能成功处理 ({missing_percentage:.2f}%)",
                f"  ⚠️  请检查错误日志以了解详细原因",
                f"  ⚠️  建议重新运行处理程序以重试失败的记录",
            ])
        else:
            stats.append("  ✅ 所有尝试处理的记录都已成功处理")

        stats.extend([
            "",
            "="*60
        ])

        # 打印到控制台和写入文件
        for stat in stats:
            write_output(stat)

        # 最终保存进度
        if processed_count > 0:
            if smart_completion_mode:
                save_progress(processed_count, total_records_found, last_processed_release_id,
                            skipped_count, smart_completion_mode)
            else:
                save_progress(processed_count, total_records_found, last_processed_release_id)
            write_output(f"✅ 最终进度已保存", False)

        # 关闭数据库连接
        client.close()

        write_output(f"\n详细输出已保存到: {OUTPUT_FILE}")
        if parsing_errors > 0:
            write_output(f"错误日志已保存到: {ERROR_FILE}")

if __name__ == "__main__":
    process_releases()
