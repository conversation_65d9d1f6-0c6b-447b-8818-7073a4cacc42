#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import gzip
import time
import re
from collections import deque

def extract_release_id(content):
    """从release标签中提取id属性"""
    pattern = r'<release[^>]*id="(\d+)"'
    match = re.search(pattern, content)
    return int(match.group(1)) if match else None

def main():
    xml_file = "data/discogs_20250701_releases.xml.gz"
    
    print("=" * 80)
    print("🎯 Release数据最后10条记录提取工具")
    print("=" * 80)
    print(f"📁 文件: {xml_file}")
    print("🚀 开始处理...")
    
    start_time = time.time()
    release_records = deque(maxlen=10)
    buffer = ""
    in_release = False
    total_records = 0
    last_progress_time = start_time
    
    try:
        with gzip.open(xml_file, 'rt', encoding='utf-8') as f:
            for line in f:
                if '<release ' in line:
                    buffer = line
                    in_release = True
                    total_records += 1
                    
                    # 每100万条记录显示进度
                    if total_records % 1000000 == 0:
                        elapsed = time.time() - start_time
                        current_id = None
                        if release_records:
                            current_id = extract_release_id(release_records[-1])
                        print(f"📊 进度: {total_records:,} 条记录 | 最大ID: {current_id} | 用时: {elapsed/60:.1f}分钟")
                        
                elif '</release>' in line and in_release:
                    buffer += line
                    in_release = False
                    release_records.append(buffer.strip())
                    buffer = ""
                    
                elif in_release:
                    buffer += line
                
                # 每5分钟显示时间进度
                current_time = time.time()
                if current_time - last_progress_time >= 300:
                    elapsed = current_time - start_time
                    current_id = None
                    if release_records:
                        current_id = extract_release_id(release_records[-1])
                    print(f"⏱️ 运行: {elapsed/60:.1f}分钟 | 记录: {total_records:,} | 最大ID: {current_id}")
                    last_progress_time = current_time
        
        # 输出结果
        scan_time = time.time() - start_time
        print(f"\n✅ 扫描完成!")
        print(f"⏱️ 总用时: {scan_time:.2f}秒 ({scan_time/60:.1f}分钟)")
        print(f"📈 总记录数: {total_records:,}")
        print(f"📋 提取记录数: {len(release_records)}")
        
        if not release_records:
            print("❌ 未找到任何记录")
            return
        
        print(f"\n{'='*80}")
        print(f"📋 最后 {len(release_records)} 条Release记录")
        print(f"{'='*80}")
        
        for i, record in enumerate(release_records, 1):
            record_id = extract_release_id(record)
            
            # 提取title
            title_match = re.search(r'<title>([^<]*)</title>', record)
            title = title_match.group(1)[:50] if title_match else "无标题"
            
            print(f"\n--- 记录 {i:2d} | ID: {record_id:>10} | Title: {title} ---")
            print(record)
            
            if i < len(release_records):
                print(f"\n{'-'*80}")
        
        print(f"\n{'='*80}")
        print(f"✅ 完成 - 共显示 {len(release_records)} 条记录")
        print(f"{'='*80}")
        
    except KeyboardInterrupt:
        print(f"\n❌ 用户中断")
        elapsed = time.time() - start_time
        print(f"⏱️ 已运行: {elapsed/60:.1f}分钟, 处理: {total_records:,} 条记录")
        
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()
