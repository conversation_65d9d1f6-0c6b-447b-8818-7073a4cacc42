# Discogs Label 数据处理工具

## 项目概述

这是一个用于处理 Discogs 音乐数据库中 Label（音乐标签/唱片公司）XML 文件的 Python 工具。该工具能够解析 Discogs labels XML 文件，提取音乐标签信息并将其存储到 MongoDB 数据库中，支持复杂的层级关系和逻辑删除功能。

### 主要功能

- 🏷️ **XML 解析**：支持解析 Discogs labels XML 文件，处理复杂的标签数据结构
- 🗄️ **数据库存储**：将解析后的数据存储到 MongoDB 数据库
- 🔗 **层级关系**：支持 parent_label 和 sublabels 的层级关系处理
- 🗑️ **逻辑删除**：内置逻辑删除机制，支持软删除功能
- 📊 **处理统计**：提供详细的处理进度和统计信息
- ✅ **结果验证**：内置验证工具检查处理结果
- 📝 **详细日志**：生成详细的处理日志文件

### 核心特性

- **Parent Label 解析**：完整解析父标签信息，包含 ID 和名称
- **Sublabels 处理**：支持子标签的嵌套结构解析
- **逻辑删除状态**：DeleteStatus 枚举管理删除状态
- **数据完整性**：保持原始 \_id 以支持数据更新
- **错误处理**：robust 的错误处理和日志记录

## 环境要求

- **Python**: 3.7+
- **MongoDB**: 4.0+
- **操作系统**: Linux, macOS, Windows

## 依赖安装

### 1. 进入项目目录

```bash
cd /path/to/discogs/label
```

### 2. 安装 Python 依赖

```bash
# 安装 pymongo
pip install pymongo

# 或者使用 pip3
pip3 install pymongo

# 如果使用虚拟环境（推荐）
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows
pip install pymongo
```

### 3. 准备数据文件

确保您有 Discogs labels XML 文件：

- 文件名：`discogs_20250501_labels.xml`
- 放置位置：项目根目录下
- 格式：标准 XML 文件

## 配置说明

### 数据库配置

编辑 `process_labels.py` 文件中的数据库配置：

```python
# 配置参数
XML_FILE = 'discogs_20250501_labels.xml'  # XML 文件路径
MONGO_URI = '********************************:port/database'  # MongoDB 连接字符串
DB_NAME = 'music_test'  # 数据库名称
MAX_RECORDS = 1000  # 最大处理记录数（用于测试，生产环境可设置更大值）
```

### 重要配置项说明

| 配置项        | 说明               | 默认值                                               | 建议                                           |
| ------------- | ------------------ | ---------------------------------------------------- | ---------------------------------------------- |
| `XML_FILE`    | XML 文件路径       | `discogs_20250501_labels.xml`                        | 根据实际文件名修改                             |
| `MONGO_URI`   | MongoDB 连接字符串 | `**********************************************************` | 修改为您的数据库信息                           |
| `DB_NAME`     | 数据库名称         | `music_test`                                         | 根据需要修改                                   |
| `MAX_RECORDS` | 最大处理记录数     | `1000`                                               | 测试时使用小值，生产时可设置为更大值或删除限制 |

## 使用方法

### 快速开始

1. **运行主处理脚本**：

```bash
python3 process_labels.py
```

2. **验证处理结果**：

```bash
python3 verify_results_final.py
```

### 详细使用步骤

#### 步骤 1：准备环境

```bash
# 检查 Python 版本
python --version

# 检查依赖是否安装
python -c "import pymongo; print('pymongo 已安装')"

# 检查 XML 文件是否存在
ls -la discogs_20250501_labels.xml
```

#### 步骤 2：配置数据库

```bash
# 测试 MongoDB 连接
python -c "
from pymongo import MongoClient
client = MongoClient('your_mongo_uri')
print('MongoDB 连接成功')
client.close()
"
```

#### 步骤 3：运行处理

```bash
# 运行主处理脚本
python3 process_labels.py
```

#### 步骤 4：验证结果

```bash
# 运行验证脚本
python3 verify_results_final.py

# 或检查子标签
python3 check_sublabels_final.py
```

### 输出文件

- **process_output.txt**：详细的处理日志文件
- **MongoDB 集合**：`label_new` - 存储处理后的数据

## 项目结构

```
discogs/label/
├── README.md                           # 项目说明文档
├── process_labels.py                   # 核心处理逻辑
├── verify_results_final.py             # 结果验证脚本
├── check_sublabels_final.py            # 子标签检查脚本
├── enums.py                           # 枚举定义
├── test_parent_label.py               # Parent Label 测试脚本
├── discogs_20250501_labels.xml        # Discogs XML 数据文件
├── process_output.txt                 # 处理日志输出（运行后生成）
└── __pycache__/                       # Python 缓存目录
```

### 核心文件说明

| 文件                       | 功能         | 说明                           |
| -------------------------- | ------------ | ------------------------------ |
| `process_labels.py`        | 核心处理逻辑 | XML 解析、数据转换、数据库存储 |
| `verify_results_final.py`  | 结果验证     | 检查处理结果的完整性和正确性   |
| `check_sublabels_final.py` | 子标签检查   | 验证子标签数据的正确性         |
| `enums.py`                 | 枚举定义     | 定义权限、状态、删除状态等枚举 |
| `test_parent_label.py`     | 测试脚本     | 测试 Parent Label 解析功能     |

## 数据结构

### Label 文档结构

```json
{
  "y_id": "YL123456",
  "id": "123456",
  "name": "标签名称",
  "profile": "标签简介和历史信息",
  "contactinfo": "联系信息",
  "data_quality": "数据质量评级",
  "sublabels": [
    {
      "id": "789",
      "text": "子标签名称"
    }
  ],
  "parent_label": {
    "id": "456",
    "name": "父标签名称"
  },
  "delete_status": 0,
  "deleted_at": null,
  "created_at": "2025-01-01T00:00:00Z",
  "updated_at": "2025-01-01T00:00:00Z",
  "source": 1,
  "permissions": 1,
  "status": 1
}
```

### 字段说明

| 字段            | 类型     | 说明                                         | 示例                           |
| --------------- | -------- | -------------------------------------------- | ------------------------------ |
| `y_id`          | String   | 系统生成的唯一标识符                         | "YL123456"                     |
| `id`            | String   | Discogs 原始 ID                              | "123456"                       |
| `name`          | String   | 标签名称                                     | "Planet E"                     |
| `profile`       | String   | 标签简介和历史信息                           | "经典 techno 标签..."          |
| `contactinfo`   | String   | 联系信息                                     | "地址、电话、邮箱等"           |
| `data_quality`  | String   | 数据质量评级                                 | "Needs Vote", "Correct"        |
| `sublabels`     | Array    | 子标签列表                                   | [{"id": "789", "text": "..."}] |
| `parent_label`  | Object   | 父标签信息（包含 id 和 name）                | {"id": "456", "name": "..."}   |
| `delete_status` | Integer  | 逻辑删除状态（0=未删除，1=已删除）           | 0                              |
| `deleted_at`    | DateTime | 删除时间戳                                   | null                           |
| `created_at`    | DateTime | 创建时间                                     | "2025-01-01T00:00:00Z"         |
| `updated_at`    | DateTime | 更新时间                                     | "2025-01-01T00:00:00Z"         |
| `source`        | Integer  | 数据来源（1=Discogs, 2=Diskunion, 3=Custom） | 1                              |
| `permissions`   | Integer  | 权限设置                                     | 1                              |
| `status`        | Integer  | 状态（1=Active, 2=Inactive, 3=Deleted）      | 1                              |

## 枚举定义

### Source（数据来源）

```python
class Source(Enum):
    DISCOGS = 1      # Discogs 数据库
    DISKUNION = 2    # Diskunion 数据库
    CUSTOM = 3       # 自定义数据
```

### Permissions（权限设置）

```python
class Permissions(Enum):
    ALL_VISIBLE = 1              # 全部可见
    RESTRICTED_VISIBILITY = 2    # 仅对某些国家/地区用户可见
    SPECIFIC_USER = 3            # 仅对特定用户可见
```

### Status（状态）

```python
class Status(Enum):
    ACTIVE = 1       # 活跃状态
    INACTIVE = 2     # 非活跃状态
    DELETED = 3      # 已删除状态
```

### DeleteStatus（逻辑删除状态）

```python
class DeleteStatus(Enum):
    NOT_DELETED = 0  # 默认状态：未删除
    DELETED = 1      # 已删除
```

## XML 标签映射

### 主要字段映射

| XML 标签         | 数据库字段     | 说明                 |
| ---------------- | -------------- | -------------------- |
| `<id>`           | `id`           | 标签 ID              |
| `<n>`            | `name`         | 标签名称             |
| `<profile>`      | `profile`      | 标签简介             |
| `<contactinfo>`  | `contactinfo`  | 联系信息             |
| `<data_quality>` | `data_quality` | 数据质量             |
| `<parentLabel>`  | `parent_label` | 父标签（解析为对象） |
| `<sublabels>`    | `sublabels`    | 子标签列表           |

### 复杂结构解析

#### Parent Label 解析

XML 格式：

```xml
<parentLabel id="4711">Goldhead Music</parentLabel>
```

解析结果：

```json
{
  "parent_label": {
    "id": "4711",
    "name": "Goldhead Music"
  }
}
```

#### Sublabels 解析

XML 格式：

```xml
<sublabels>
  <label id="31405">I Ner Zon Sounds</label>
  <label id="41841">Community Projects</label>
</sublabels>
```

解析结果：

```json
{
  "sublabels": [
    { "id": "31405", "text": "I Ner Zon Sounds" },
    { "id": "41841", "text": "Community Projects" }
  ]
}
```

## 故障排除

### 常见问题

#### 1. 找不到 XML 文件

```
错误: 找不到XML文件 discogs_20250501_labels.xml
```

**解决方案**：

- 确保 XML 文件在项目根目录下
- 检查文件名是否正确
- 如果文件名不同，修改 `process_labels.py` 中的 `XML_FILE` 配置

#### 2. MongoDB 连接失败

```
pymongo.errors.ServerSelectionTimeoutError
```

**解决方案**：

- 检查 MongoDB 服务是否运行
- 验证连接字符串是否正确
- 检查网络连接和防火墙设置
- 确认用户名和密码是否正确

#### 3. 依赖安装失败

```
ModuleNotFoundError: No module named 'pymongo'
```

**解决方案**：

```bash
pip install pymongo
# 或
pip3 install pymongo
```

#### 4. Parent Label 解析失败

如果 parent_label 字段为空：

- 检查 XML 文件中 `<parentLabel>` 标签格式
- 确认正则表达式是否匹配
- 运行测试脚本：`python3 test_parent_label.py`

#### 5. 内存不足

如果处理大文件时出现内存问题：

- 减少 `MAX_RECORDS` 的值
- 分批处理数据
- 增加系统内存

#### 6. 权限问题

```
PermissionError: [Errno 13] Permission denied
```

**解决方案**：

- 检查文件和目录权限
- 使用 `chmod` 命令修改权限（Linux/macOS）
- 以管理员身份运行（Windows）

### 调试技巧

1. **启用详细日志**：

   - 查看 `process_output.txt` 文件
   - 在代码中添加更多 `write_output()` 调用

2. **测试小数据集**：

   - 将 `MAX_RECORDS` 设置为较小值（如 10）
   - 使用测试 XML 文件

3. **检查数据库状态**：

   ```bash
   python3 verify_results_final.py
   ```

4. **手动检查数据库**：

   ```python
   from pymongo import MongoClient
   client = MongoClient('your_mongo_uri')
   db = client['music_test']
   print(db.label_new.count_documents({}))

   # 检查 parent_label 数据
   parent_labels = db.label_new.find({'parent_label': {'$ne': None}})
   for label in parent_labels.limit(5):
       print(f"ID: {label['id']}, Parent: {label['parent_label']}")
   ```

5. **测试 Parent Label 解析**：
   ```bash
   python3 test_parent_label.py
   ```

## 性能优化

### 处理大文件的建议

1. **调整批处理大小**：

   - 修改 `MAX_RECORDS` 参数
   - 分批处理大型 XML 文件

2. **数据库优化**：

   - 为常用查询字段创建索引
   - 使用批量插入操作

3. **内存管理**：
   - 监控内存使用情况
   - 适当清理缓冲区

### 索引建议

```javascript
// MongoDB 索引创建建议
db.label_new.createIndex({ id: 1 });
db.label_new.createIndex({ y_id: 1 });
db.label_new.createIndex({ "parent_label.id": 1 });
db.label_new.createIndex({ delete_status: 1 });
db.label_new.createIndex({ status: 1 });
```

## 数据查询示例

### 基本查询

```python
from pymongo import MongoClient

client = MongoClient('your_mongo_uri')
db = client['music_test']
collection = db['label_new']

# 查找所有活跃标签
active_labels = collection.find({'status': 1, 'delete_status': 0})

# 查找有父标签的标签
labels_with_parent = collection.find({'parent_label': {'$ne': None}})

# 查找特定标签的子标签
sublabels = collection.find({'parent_label.id': '123456'})

# 统计各种状态的标签数量
status_stats = collection.aggregate([
    {'$group': {'_id': '$status', 'count': {'$sum': 1}}}
])
```

### 复杂查询

```python
# 查找有子标签的标签
labels_with_sublabels = collection.find({
    'sublabels': {'$exists': True, '$ne': []}
})

# 查找特定数据质量的标签
quality_labels = collection.find({
    'data_quality': 'Correct'
})

# 按创建时间排序
recent_labels = collection.find().sort('created_at', -1).limit(10)
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

### 开发规范

- 遵循 PEP 8 Python 代码规范
- 添加适当的注释和文档字符串
- 编写单元测试
- 更新 README 文档

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 创建 Issue
- 发送邮件至：[<EMAIL>]

## 更新日志

### v1.2.0 (2025-01-26)

- ✨ 新增逻辑删除功能（DeleteStatus 枚举）
- 🔧 修复 parent_label 解析问题
- 📦 优化数据结构，parent_label 存储为完整对象
- 🐛 修复 XML 标签映射问题（`<n>` 到 `name` 字段）
- 📝 完善文档和使用指南

### v1.1.0 (2025-01-01)

- 🔧 优化 sublabels 解析逻辑
- 📊 改进处理统计和日志输出
- 🛠️ 添加验证和检查脚本

### v1.0.0 (2025-01-01)

- 🎉 初始版本发布
- 🏷️ 支持 Discogs labels XML 文件解析
- 🗄️ MongoDB 数据存储功能
- 🔗 Parent Label 和 Sublabels 层级关系处理
- ✅ 结果验证工具

## 技术支持

### 常用命令速查

```bash
# 快速启动
python3 process_labels.py

# 验证结果
python3 verify_results_final.py

# 检查子标签
python3 check_sublabels_final.py

# 测试 Parent Label 解析
python3 test_parent_label.py

# 检查数据库连接
python3 -c "from pymongo import MongoClient; print('连接成功')"
```

### 环境检查清单

- [ ] Python 3.7+ 已安装
- [ ] pymongo 依赖已安装
- [ ] MongoDB 服务正在运行
- [ ] XML 文件存在且可读
- [ ] 数据库连接配置正确
- [ ] 有足够的磁盘空间和内存

---

**感谢使用 Discogs Label 数据处理工具！** 🎵
