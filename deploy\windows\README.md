# Discogs 数据处理工具 - Windows 长时间运行部署指南

## 概述

本工具用于处理 Discogs XML 数据文件（gzip 压缩格式），支持四个模块的独立长时间运行：

- **Artists** (艺术家) - 数据量中等，预计运行 1-2 天
- **Labels** (厂牌) - 数据量中等，预计运行 2-3 天
- **Masters** (母版) - 数据量中等，预计运行 1-2 天
- **Releases** (发行版) - 数据量最大，预计运行 15 天左右

每个模块都支持独立配置、监控和管理，适合在 Windows 服务器环境下长时间稳定运行。

## 系统要求

- Windows 10/11 或 Windows Server 2016+
- Python 3.8 或更高版本
- MongoDB 数据库（可以是远程或本地）
- 至少 2GB 可用内存（处理大文件时）
- 足够的磁盘空间存储 gzip 文件和输出日志

## 快速部署

### 1. 准备工作

1. **安装 Python**：https://www.python.org/downloads/

   - 安装时勾选 "Add Python to PATH"
   - 建议安装 Python 3.8 或更高版本

2. **准备数据文件**：将四个 gzip 数据文件放置到指定位置（详见下方说明）

3. **复制项目文件**：将整个部署包复制到 Windows 服务器

### 📁 gzip 数据文件放置位置

**推荐方式：放在 `data/` 目录下**

```
discogs/deploy/windows/
└── data/
    ├── discogs_20250601_artists.xml.gz
    ├── discogs_20250601_labels.xml.gz
    ├── discogs_20250601_masters.xml.gz
    └── discogs_20250601_releases.xml.gz
```

**备选方式：直接放在部署目录下**

```
discogs/deploy/windows/
├── discogs_20250601_artists.xml.gz
├── discogs_20250601_labels.xml.gz
├── discogs_20250601_masters.xml.gz
├── discogs_20250601_releases.xml.gz
└── ...其他文件
```

**检查文件是否正确放置：**

```cmd
check_files.bat
```

### 2. 一键初始化

```cmd
setup.bat
```

### 3. 配置各模块参数

编辑对应的配置文件：

- `config_artists.env` - 艺术家模块配置
- `config_labels.env` - 厂牌模块配置
- `config_masters.env` - 母版模块配置
- `config_releases.env` - 发行版模块配置

关键配置项：

```env
# MongoDB 连接
MONGO_URI=********************************:port/database
DB_NAME=your_database_name

# 处理批次大小（根据数据量调整）
MAX_RECORDS=50000

# 长时间运行配置
AUTO_RESTART=true
MAX_MEMORY_MB=2048
```

### 4. 启动处理

**🚀 方式一：一键快速启动（最简单）**

```cmd
start_all_quick.bat
```

- 自动检查环境和数据文件
- 一次性启动所有 4 个模块
- 每个模块在独立窗口运行

**🎛️ 方式二：使用管理界面（功能最全）**

```cmd
manage_all.bat
```

- 提供完整的管理功能
- 可以单独启动/停止模块
- 包含状态检查和监控功能

**⚡ 方式三：单独启动模块**

```cmd
run_module.bat artists
run_module.bat labels
run_module.bat masters
run_module.bat releases
```

**📊 方式四：系统监控**

```cmd
python monitor.py              # 单次检查
python monitor.py --continuous # 持续监控
```

## 详细配置说明

### 环境变量配置

| 变量名        | 说明               | Artists                                            | Labels                         | Masters                         | Releases                         |
| ------------- | ------------------ | -------------------------------------------------- | ------------------------------ | ------------------------------- | -------------------------------- |
| XML_FILE      | gzip 数据文件路径  | discogs_20250601_artists.xml.gz                    | discogs_20250601_labels.xml.gz | discogs_20250601_masters.xml.gz | discogs_20250601_releases.xml.gz |
| MONGO_URI     | MongoDB 连接字符串 | ********************************************************** | 同左                           | 同左                            | 同左                             |
| DB_NAME       | 数据库名称         | music_test                                         | 同左                           | 同左                            | 同左                             |
| MAX_RECORDS   | 最大处理记录数     | 50000                                              | 30000                          | 20000                           | 10000                            |
| OUTPUT_FILE   | 输出日志文件       | logs/artists_process_output.txt                    | logs/labels_process_output.txt | logs/masters_process_output.txt | logs/releases_process_output.txt |
| AUTO_RESTART  | 自动重启           | true                                               | true                           | true                            | true                             |
| MAX_MEMORY_MB | 最大内存限制       | 2048                                               | 2048                           | 2048                            | 4096                             |

### 📊 系统监控功能

**实时监控系统资源：**

- CPU 使用率监控
- 内存使用情况跟踪
- 磁盘空间检查
- Python 进程状态监控

**监控使用方法：**

```cmd
# 单次检查系统状态
python monitor.py

# 持续监控（每60秒检查一次）
python monitor.py --continuous

# 在管理界面中查看
manage_all.bat
# 选择选项 7 - 系统资源监控
```

**告警功能：**

- 内存使用率超过 80% 时告警
- 磁盘使用率超过 90% 时告警
- CPU 使用率超过 90% 时告警
- 单个进程内存超过 2GB 时告警

### gzip 文件处理

- 支持大文件处理（GB 级别）
- 内存效率优化，逐行读取
- 自动处理 UTF-8 编码
- 支持相对路径和绝对路径

### MongoDB 连接

- 支持本地和远程 MongoDB
- 支持认证和非认证连接
- 自动创建集合和索引
- 支持连接池和重连机制

## 高级部署选项

### 作为 Windows 服务运行

1. 下载 NSSM (Non-Sucking Service Manager)：https://nssm.cc/download
2. 解压到系统 PATH 或当前目录
3. 以管理员身份运行命令提示符
4. 安装服务：
   ```cmd
   nssm install "DiscogsArtistsProcessor" "%CD%\run.bat"
   nssm set "DiscogsArtistsProcessor" AppDirectory "%CD%"
   nssm set "DiscogsArtistsProcessor" Description "Discogs Artists Data Processor"
   nssm start "DiscogsArtistsProcessor"
   ```

### 定时任务

使用 Windows 任务计划程序设置定时执行：

1. 打开"任务计划程序"
2. 创建基本任务
3. 设置触发器（每日、每周等）
4. 操作选择"启动程序"，程序路径设为 `run.bat` 的完整路径

### 性能优化

1. **内存优化**：

   - 调整 `MAX_RECORDS` 参数控制批处理大小
   - 监控内存使用情况

2. **磁盘 I/O 优化**：

   - 将 gzip 文件放在 SSD 上
   - 输出日志文件放在不同磁盘

3. **网络优化**：
   - MongoDB 连接使用连接池
   - 考虑使用本地 MongoDB 实例

## 故障排除

### 常见问题

1. **Python 未找到**

   - 确保 Python 已正确安装并添加到 PATH
   - 重启命令提示符或重新登录

2. **虚拟环境创建失败**

   - 检查磁盘空间
   - 确保有写入权限
   - 尝试以管理员身份运行

3. **MongoDB 连接失败**

   - 检查网络连接
   - 验证连接字符串格式
   - 确认 MongoDB 服务运行状态

4. **gzip 文件读取错误**

   - 检查文件是否存在
   - 验证文件完整性
   - 确认文件路径正确

5. **内存不足**
   - 减少 `MAX_RECORDS` 值
   - 增加系统虚拟内存
   - 关闭其他占用内存的程序

### 日志分析

- 详细日志保存在 `OUTPUT_FILE` 指定的文件中
- 控制台显示处理进度和统计信息
- 错误信息会同时输出到控制台和日志文件

### 性能监控

- 使用任务管理器监控 CPU 和内存使用
- 监控磁盘 I/O 性能
- 检查 MongoDB 连接状态

## 安全注意事项

1. **数据库安全**：

   - 使用强密码
   - 限制数据库访问权限
   - 考虑使用 SSL 连接

2. **文件权限**：

   - 确保数据文件的读取权限
   - 限制输出目录的写入权限

3. **网络安全**：
   - 配置防火墙规则
   - 使用 VPN 或专用网络连接数据库

## 联系支持

如遇到问题，请提供以下信息：

- Windows 版本和架构
- Python 版本
- 错误日志内容
- 配置文件内容（隐藏敏感信息）
