#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查数据库中release_new表的当前状态
查询记录数量、最后一条记录信息等
"""

import os
from pymongo import MongoClient
from datetime import datetime

def connect_to_mongodb():
    """连接到MongoDB"""
    try:
        MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
        DB_NAME = os.getenv('DB_NAME', 'music_test')
        
        client = MongoClient(MONGO_URI)
        client.admin.command('ping')
        return client, client[DB_NAME]
    except Exception as e:
        print(f"❌ MongoDB连接失败: {e}")
        return None, None

def get_database_status():
    """获取数据库状态信息"""
    client, db = connect_to_mongodb()
    if client is None or db is None:
        return
    
    try:
        collection = db['release_new']
        
        print("=" * 80)
        print("📊 RELEASE_NEW 数据库状态报告")
        print("=" * 80)
        print()
        
        # 1. 基本统计信息
        total_count = collection.count_documents({})
        print(f"🔢 总记录数: {total_count:,}")
        
        if total_count == 0:
            print("❌ 数据库为空，没有任何记录")
            return
        
        # 2. ID范围分析
        print("\n📈 ID范围分析:")
        
        # 获取最小ID
        pipeline_min = [
            {'$addFields': {'id_numeric': {'$toInt': '$id'}}},
            {'$sort': {'id_numeric': 1}},
            {'$limit': 1},
            {'$project': {'id': 1, 'y_id': 1, 'title': 1}}
        ]
        
        min_result = list(collection.aggregate(pipeline_min))
        if min_result:
            min_record = min_result[0]
            print(f"  • 最小ID: {min_record['id']}")
            print(f"    - y_id: {min_record.get('y_id', 'N/A')}")
            print(f"    - title: {min_record.get('title', 'N/A')[:50]}...")
        
        # 获取最大ID
        pipeline_max = [
            {'$addFields': {'id_numeric': {'$toInt': '$id'}}},
            {'$sort': {'id_numeric': -1}},
            {'$limit': 1},
            {'$project': {'id': 1, 'y_id': 1, 'title': 1, 'created_at': 1}}
        ]
        
        max_result = list(collection.aggregate(pipeline_max))
        if max_result:
            max_record = max_result[0]
            print(f"  • 最大ID: {max_record['id']}")
            print(f"    - y_id: {max_record.get('y_id', 'N/A')}")
            print(f"    - title: {max_record.get('title', 'N/A')[:50]}...")
            if 'created_at' in max_record:
                print(f"    - 创建时间: {max_record['created_at']}")
        
        # 3. 最近10条记录
        print("\n📋 最近10条记录:")
        recent_records = list(collection.find({}, {'id': 1, 'y_id': 1, 'title': 1}).sort([('_id', -1)]).limit(10))
        
        for i, record in enumerate(recent_records, 1):
            title = record.get('title', 'N/A')[:30] if record.get('title') else 'N/A'
            print(f"  {i:2d}. ID: {record['id']:>8} | y_id: {record.get('y_id', 'N/A'):>8} | title: {title}...")
        
        # 4. ID分布分析
        print("\n📊 ID分布分析:")
        
        # 按ID范围统计
        ranges = [
            (1, 1000000),
            (1000001, 2000000),
            (2000001, 3000000),
            (3000001, 4000000),
            (4000001, 5000000),
            (5000001, 6000000),
            (6000001, 7000000),
            (7000001, 8000000),
            (8000001, 9000000),
            (9000001, 10000000),
            (10000001, 15000000),
            (15000001, 20000000),
            (20000001, 25000000)
        ]
        
        for start, end in ranges:
            count = collection.count_documents({
                '$expr': {
                    '$and': [
                        {'$gte': [{'$toInt': '$id'}, start]},
                        {'$lte': [{'$toInt': '$id'}, end]}
                    ]
                }
            })
            if count > 0:
                print(f"  • ID {start:>8,} - {end:>8,}: {count:>8,} 条记录")
        
        # 5. 数据完整性检查
        print("\n🔍 数据完整性检查:")
        
        # 检查必要字段
        fields_to_check = ['id', 'title', 'y_id']
        for field in fields_to_check:
            null_count = collection.count_documents({field: {'$in': [None, '']}})
            if null_count > 0:
                print(f"  ⚠️  字段 '{field}' 有 {null_count:,} 条记录为空")
            else:
                print(f"  ✅ 字段 '{field}' 完整")
        
        # 6. 预期数据量对比
        print("\n📈 数据完整性评估:")
        expected_total = 18000000  # 预期约1800万条记录
        completion_rate = (total_count / expected_total) * 100
        missing_count = expected_total - total_count
        
        print(f"  • 预期总记录数: {expected_total:,}")
        print(f"  • 当前记录数: {total_count:,}")
        print(f"  • 缺失记录数: {missing_count:,}")
        print(f"  • 完成度: {completion_rate:.1f}%")
        
        if completion_rate < 50:
            print("  🚨 严重警告: 数据完整性严重不足，需要重新处理")
        elif completion_rate < 80:
            print("  ⚠️  警告: 数据不完整，建议检查处理逻辑")
        else:
            print("  ✅ 数据相对完整")
        
        print("\n" + "=" * 80)
        
    except Exception as e:
        print(f"❌ 查询数据库时出错: {e}")
    finally:
        if client:
            client.close()

if __name__ == "__main__":
    get_database_status()
