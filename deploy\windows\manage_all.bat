@echo off
chcp 936 >nul
echo ========================================
echo Discogs Data Processing Tool - Multi-Module Manager
echo ========================================

cd /d "%~dp0"

:MAIN_MENU
echo.
echo Please select operation:
echo.
echo 1. View all module status
echo 2. Start single module
echo 3. Start all modules (parallel)
echo 4. Stop all modules
echo 5. View running logs
echo 6. Clean logs and progress files
echo 7. System resource monitoring
echo 8. Configuration management
echo 9. RELEASES Complete Reset and Reprocess
echo 0. Exit
echo.
set /p choice="Please enter choice (0-9): "

if "%choice%"=="1" goto STATUS
if "%choice%"=="2" goto START_SINGLE
if "%choice%"=="3" goto START_ALL
if "%choice%"=="4" goto STOP_ALL
if "%choice%"=="5" goto VIEW_LOGS
if "%choice%"=="6" goto CLEANUP
if "%choice%"=="7" goto MONITOR
if "%choice%"=="8" goto CONFIG
if "%choice%"=="9" goto RELEASES_RESET
if "%choice%"=="0" goto EXIT
goto MAIN_MENU

:STATUS
echo.
echo ========================================
echo Module Status Check
echo ========================================
echo.

echo Checking running processes...
tasklist /fi "imagename eq python.exe" /fo table 2>nul | findstr python.exe >nul
if %errorlevel%==0 (
    echo [OK] Found Python processes running
    tasklist /fi "imagename eq python.exe" /fo table
) else (
    echo [X] No running Python processes found
)

echo.
echo Checking configuration files:
for %%m in (artists labels masters releases) do (
    if exist "config_%%m.env" (
        echo [OK] config_%%m.env
    ) else (
        echo [X] config_%%m.env
    )
)

echo.
echo Checking log files:
if exist "logs" (
    echo [DIR] logs directory exists
    dir logs\*.txt /b 2>nul
) else (
    echo [X] logs directory does not exist
)

pause
goto MAIN_MENU

:START_SINGLE
echo.
echo Available modules:
echo 1. artists  (Artists)
echo 2. labels   (Labels)
echo 3. masters  (Masters)
echo 4. releases (Releases)
echo.
set /p module_choice="Please select module (1-4): "

if "%module_choice%"=="1" set MODULE_NAME=artists
if "%module_choice%"=="2" set MODULE_NAME=labels
if "%module_choice%"=="3" set MODULE_NAME=masters
if "%module_choice%"=="4" set MODULE_NAME=releases

if "%MODULE_NAME%"=="" (
    echo Invalid selection
    goto START_SINGLE
)

echo.
echo Starting %MODULE_NAME% module...
start "Discogs-%MODULE_NAME%" cmd /k "run_module.bat %MODULE_NAME%"
echo [OK] %MODULE_NAME% module started in new window

pause
goto MAIN_MENU

:START_ALL
echo.
echo ========================================
echo Start All Modules (Parallel)
echo ========================================
echo.
echo [WARNING] Running all modules in parallel will consume significant system resources
echo    Recommend ensuring sufficient memory and CPU resources
echo.
set /p confirm="Confirm to start all modules? (y/N): "
if /i not "%confirm%"=="y" goto MAIN_MENU

echo.
echo Starting all modules...

start "Discogs-Artists" cmd /k "run_module.bat artists"
timeout /t 2 /nobreak >nul

start "Discogs-Labels" cmd /k "run_module.bat labels"
timeout /t 2 /nobreak >nul

start "Discogs-Masters" cmd /k "run_module.bat masters"
timeout /t 2 /nobreak >nul

start "Discogs-Releases" cmd /k "run_module.bat releases"

echo.
echo [OK] All modules started, please check their respective windows
echo [TIP] Recommend using Task Manager to monitor system resource usage

pause
goto MAIN_MENU

:STOP_ALL
echo.
echo ========================================
echo Stop All Modules
echo ========================================
echo.
echo [WARNING] This will forcefully terminate all Python processes
set /p confirm="Confirm to stop all modules? (y/N): "
if /i not "%confirm%"=="y" goto MAIN_MENU

echo.
echo Stopping all Python processes...
taskkill /f /im python.exe 2>nul
if %errorlevel%==0 (
    echo [OK] All Python processes stopped
) else (
    echo [INFO] No running Python processes found
)

pause
goto MAIN_MENU

:VIEW_LOGS
echo.
echo ========================================
echo View Running Logs
echo ========================================
echo.

if not exist "logs" (
    echo [X] logs directory does not exist
    pause
    goto MAIN_MENU
)

echo Available log files:
echo.
dir logs\*.txt /b 2>nul
if %errorlevel% neq 0 (
    echo [X] No log files found
    pause
    goto MAIN_MENU
)

echo.
echo Opening latest log file...
for %%f in (logs\*.txt) do (
    notepad "%%f"
    goto MAIN_MENU
)

pause
goto MAIN_MENU

:CLEANUP
echo.
echo ========================================
echo Clean Logs and Progress Files
echo ========================================
echo.
echo [WARNING] This will delete all log and progress files
echo    After deleting progress files, processing will start from beginning
echo.
set /p confirm="Confirm cleanup? (y/N): "
if /i not "%confirm%"=="y" goto MAIN_MENU

echo.
echo Cleaning files...

if exist "logs" (
    del /q logs\*.* 2>nul
    echo [OK] Cleaned logs directory
)

if exist "progress" (
    del /q progress\*.* 2>nul
    echo [OK] Cleaned progress directory
)

echo [OK] Cleanup complete

pause
goto MAIN_MENU

:MONITOR
echo.
echo ========================================
echo System Resource Monitoring
echo ========================================
echo.

echo [MEM] Memory usage:
wmic OS get TotalVisibleMemorySize,FreePhysicalMemory /format:table

echo.
echo [DISK] Disk space:
wmic logicaldisk get size,freespace,caption /format:table

echo.
echo [CPU] CPU usage:
wmic cpu get loadpercentage /value

echo.
echo [PROC] Python processes:
tasklist /fi "imagename eq python.exe" /fo table 2>nul

pause
goto MAIN_MENU

:CONFIG
echo.
echo ========================================
echo Configuration Management
echo ========================================
echo.
echo 1. Edit Artists configuration
echo 2. Edit Labels configuration  
echo 3. Edit Masters configuration
echo 4. Edit Releases configuration
echo 5. Return to main menu
echo.
set /p config_choice="Please select (1-5): "

if "%config_choice%"=="1" notepad config_artists.env
if "%config_choice%"=="2" notepad config_labels.env
if "%config_choice%"=="3" notepad config_masters.env
if "%config_choice%"=="4" notepad config_releases.env
if "%config_choice%"=="5" goto MAIN_MENU

goto CONFIG

:RELEASES_RESET
echo.
echo ================================================================
echo RELEASES Complete Reset and Reprocess
echo ================================================================
echo.
echo This function will perform the following operations:
echo 1. Verify current data integrity status
echo 2. Clean releases related progress files and logs
echo 3. Reset environment configuration to start from release ID 1
echo 4. Start complete releases data processing
echo.
echo IMPORTANT NOTES:
echo   - XML file contains 18,333,401 records
echo   - Estimated processing time: 6-12 hours
echo   - Supports checkpoint resume, can interrupt and resume anytime
echo   - Automatically checks duplicate records to avoid duplication
echo.
set /p confirm="Confirm to start complete releases reset and reprocess? (y/N): "
if /i not "%confirm%"=="y" goto MAIN_MENU

echo.
echo Step 1: Verify current data integrity...
echo ================================================================
if exist "verify_releases_integrity.py" (
    python verify_releases_integrity.py
    echo.
    echo Press any key to continue reset process...
    pause >nul
) else (
    echo WARNING: Data integrity verification script not found, skipping verification
)

echo.
echo Step 2: Clean releases environment...
echo ================================================================

REM Delete releases related progress files
if exist "progress\releases_progress.json" (
    del "progress\releases_progress.json"
    echo [OK] Deleted releases progress file
) else (
    echo [INFO] Releases progress file does not exist
)

REM Delete releases related error logs
if exist "logs\releases_errors.log" (
    del "logs\releases_errors.log"
    echo [OK] Deleted releases error log
) else (
    echo [INFO] Releases error log does not exist
)

REM Delete releases output files
if exist "process_output.txt" (
    del "process_output.txt"
    echo [OK] Deleted releases output file
) else (
    echo [INFO] Releases output file does not exist
)

if exist "logs\releases_process_output.txt" (
    del "logs\releases_process_output.txt"
    echo [OK] Deleted releases process log
) else (
    echo [INFO] Releases process log does not exist
)

echo.
echo Step 3: Configure environment parameters...
echo ================================================================

REM Set environment variables to start from ID 1
set START_RELEASE_ID=1
set MAX_RECORDS=0
set CHECKPOINT_INTERVAL=1000
set AUTO_RESTART=false

echo [OK] Environment parameters set:
echo   - Start release ID: 1
echo   - Max records: unlimited
echo   - Checkpoint interval: 1000 records
echo   - Auto restart: disabled

echo.
echo Step 4: Start releases processing...
echo ================================================================
echo.
echo About to start releases data processing, details:
echo   - Data source: XML file (18,333,401 records)
echo   - Target database: release_new collection
echo   - Processing strategy: Check duplicates, avoid duplicate insertion
echo   - Error handling: Smart retry, detailed logging
echo   - Progress saving: Auto save every 1000 records
echo.
echo Processing tips:
echo   - Can interrupt with Ctrl+C anytime
echo   - After interruption, can rerun this option to continue
echo   - Detailed logs saved to logs\releases_process_output.txt
echo   - Error logs saved to logs\releases_errors.log
echo.

set /p start_confirm="Confirm to start processing? (y/N): "
if /i not "%start_confirm%"=="y" (
    echo [CANCEL] Processing cancelled, returning to main menu
    goto MAIN_MENU
)

echo.
echo Starting releases processing...
echo ================================================================

REM Ensure necessary directories exist
if not exist "logs" mkdir logs
if not exist "progress" mkdir progress

REM Start releases processing
echo [%DATE% %TIME%] Starting releases complete processing...
start "Discogs-Releases-Complete" cmd /k "echo RELEASES Complete Processing Window && echo. && echo Processing Parameters: && echo   Start ID: 1 && echo   Target: release_new collection && echo   Strategy: Complete reprocessing && echo. && set START_RELEASE_ID=1 && set MAX_RECORDS=0 && set CHECKPOINT_INTERVAL=1000 && python process_releases.py"

echo.
echo [OK] Releases processing started in new window
echo [TIP] Please check the new window to monitor processing progress
echo [INFO] Recommend checking processing status and log files regularly
echo.

pause
goto MAIN_MENU

:EXIT
echo.
echo Thank you for using Discogs Data Processing Tool!
echo.
pause
exit /b 0
