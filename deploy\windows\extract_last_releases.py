#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
高效提取release gzip数据中最后10条记录的脚本
优化性能，支持大文件处理，提供详细的进度和输出信息
"""

import gzip
import time
import re
import os
from collections import deque
from datetime import datetime

def extract_release_id(content):
    """从release标签中提取id属性"""
    pattern = r'<release[^>]*id="(\d+)"'
    match = re.search(pattern, content)
    return int(match.group(1)) if match else None

def format_file_size(size_bytes):
    """格式化文件大小显示"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.2f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.2f} TB"

def extract_last_releases(xml_file, n=10):
    """
    高效提取XML文件中最后N条release记录
    
    Args:
        xml_file: XML文件路径
        n: 要提取的记录数量
    
    Returns:
        list: 最后N条完整的release记录（XML格式）
    """
    print("=" * 80)
    print("🎯 Release记录提取工具")
    print("=" * 80)
    
    # 检查文件是否存在
    if not os.path.exists(xml_file):
        print(f"❌ 错误: 文件不存在 - {xml_file}")
        return []
    
    # 获取文件信息
    file_size = os.path.getsize(xml_file)
    print(f"📁 文件路径: {xml_file}")
    print(f"📊 文件大小: {format_file_size(file_size)}")
    print(f"🎯 目标记录数: {n} 条")
    
    start_time = time.time()
    release_records = deque(maxlen=n)  # 使用固定大小的队列
    buffer = ""
    in_release = False
    total_records = 0
    
    # 进度显示相关变量
    last_progress_time = start_time
    progress_interval = 30  # 每30秒显示一次进度
    record_interval = 100000  # 每10万条记录显示一次进度
    
    print(f"\n🚀 开始扫描文件...")
    print(f"📈 进度显示: 每 {record_interval:,} 条记录或每 {progress_interval} 秒")
    
    try:
        with gzip.open(xml_file, 'rt', encoding='utf-8') as f:
            for line in f:
                # 检测release标签开始
                if '<release ' in line:
                    buffer = line
                    in_release = True
                    total_records += 1
                    
                    # 每10万条记录显示进度
                    if total_records % record_interval == 0:
                        elapsed_time = time.time() - start_time
                        current_max_id = None
                        if release_records:
                            current_max_id = extract_release_id(release_records[-1])
                        
                        print(f"📊 进度: {total_records:,} 条记录 | "
                              f"当前最大ID: {current_max_id or 'N/A'} | "
                              f"用时: {elapsed_time/60:.1f}分钟")
                        
                elif '</release>' in line and in_release:
                    buffer += line
                    in_release = False
                    
                    # 保存完整的release记录
                    release_records.append(buffer.strip())
                    
                    # 清空缓冲区
                    buffer = ""
                    
                elif in_release:
                    buffer += line
                
                # 定期显示时间进度
                current_time = time.time()
                if current_time - last_progress_time >= progress_interval:
                    elapsed_time = current_time - start_time
                    current_max_id = None
                    if release_records:
                        current_max_id = extract_release_id(release_records[-1])
                    
                    print(f"⏱️ 时间进度: 已扫描 {total_records:,} 条记录 | "
                          f"当前最大ID: {current_max_id or 'N/A'} | "
                          f"用时: {elapsed_time/60:.1f}分钟")
                    last_progress_time = current_time
    
    except Exception as e:
        print(f"❌ 读取过程中出错: {e}")
        return []
    
    scan_time = time.time() - start_time
    
    # 输出统计信息
    print(f"\n" + "=" * 80)
    print(f"📊 扫描完成统计")
    print(f"=" * 80)
    print(f"✅ 扫描用时: {scan_time:.2f} 秒 ({scan_time/60:.1f} 分钟)")
    print(f"📈 总记录数: {total_records:,} 条")
    print(f"📋 提取记录: {len(release_records)} 条")
    
    if total_records > 0:
        avg_time_per_record = scan_time / total_records
        records_per_second = total_records / scan_time
        print(f"⚡ 处理速度: {records_per_second:.0f} 记录/秒")
        print(f"📊 平均时间: {avg_time_per_record*1000:.4f} 毫秒/记录")
    
    return list(release_records)

def display_records(release_records):
    """显示提取的记录"""
    if not release_records:
        print("❌ 没有记录可显示")
        return
    
    print(f"\n" + "=" * 80)
    print(f"📋 最后 {len(release_records)} 条Release记录")
    print(f"=" * 80)
    
    for i, record in enumerate(release_records, 1):
        # 提取release ID
        record_id = extract_release_id(record)
        
        # 提取title（简单方式）
        title_match = re.search(r'<title>([^<]*)</title>', record)
        title = title_match.group(1)[:60] if title_match else "无标题"
        
        print(f"\n--- 记录 {i:2d} | ID: {record_id:>10} | Title: {title} ---")
        print(record)
        
        if i < len(release_records):
            print("\n" + "-" * 80)
    
    print(f"\n" + "=" * 80)
    print(f"✅ 显示完成 - 共 {len(release_records)} 条记录")
    print(f"🕒 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"=" * 80)

def main():
    """主函数"""
    # 文件路径
    xml_file = "data/discogs_20250701_releases.xml.gz"
    
    # 提取最后10条记录
    release_records = extract_last_releases(xml_file, 10)
    
    if release_records:
        # 显示记录
        display_records(release_records)
    else:
        print("❌ 未能提取到任何记录")

if __name__ == "__main__":
    main()
