#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统资源监控脚本
用于监控长时间运行的 Discogs 数据处理任务
"""

import psutil
import time
import json
import os
import sys
from datetime import datetime, timedelta
import logging

class SystemMonitor:
    def __init__(self, config_file="monitor_config.json"):
        self.config_file = config_file
        self.load_config()
        self.setup_logging()
        
    def load_config(self):
        """加载监控配置"""
        default_config = {
            "check_interval": 60,  # 检查间隔（秒）
            "memory_threshold": 80,  # 内存使用率阈值（%）
            "disk_threshold": 90,   # 磁盘使用率阈值（%）
            "cpu_threshold": 90,    # CPU使用率阈值（%）
            "log_file": "logs/monitor.log",
            "alert_file": "logs/alerts.log",
            "enable_email_alerts": False,
            "email_config": {
                "smtp_server": "",
                "smtp_port": 587,
                "username": "",
                "password": "",
                "to_emails": []
            }
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
            except Exception as e:
                print(f"加载配置文件失败: {e}，使用默认配置")
        
        self.config = default_config
        
        # 保存配置文件（如果不存在）
        if not os.path.exists(self.config_file):
            self.save_config()
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def setup_logging(self):
        """设置日志"""
        os.makedirs(os.path.dirname(self.config["log_file"]), exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.config["log_file"], encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def get_system_info(self):
        """获取系统信息"""
        try:
            # CPU 信息
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            
            # 内存信息
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used_gb = memory.used / (1024**3)
            memory_total_gb = memory.total / (1024**3)
            
            # 磁盘信息
            disk = psutil.disk_usage('.')
            disk_percent = (disk.used / disk.total) * 100
            disk_free_gb = disk.free / (1024**3)
            disk_total_gb = disk.total / (1024**3)
            
            # Python 进程信息
            python_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'cpu_percent']):
                try:
                    if 'python' in proc.info['name'].lower():
                        python_processes.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'memory_mb': proc.info['memory_info'].rss / (1024**2),
                            'cpu_percent': proc.info['cpu_percent']
                        })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            return {
                'timestamp': datetime.now().isoformat(),
                'cpu': {
                    'percent': cpu_percent,
                    'count': cpu_count
                },
                'memory': {
                    'percent': memory_percent,
                    'used_gb': round(memory_used_gb, 2),
                    'total_gb': round(memory_total_gb, 2)
                },
                'disk': {
                    'percent': round(disk_percent, 2),
                    'free_gb': round(disk_free_gb, 2),
                    'total_gb': round(disk_total_gb, 2)
                },
                'python_processes': python_processes
            }
        except Exception as e:
            self.logger.error(f"获取系统信息失败: {e}")
            return None
    
    def check_thresholds(self, system_info):
        """检查阈值并生成告警"""
        alerts = []
        
        if system_info['cpu']['percent'] > self.config['cpu_threshold']:
            alerts.append(f"CPU 使用率过高: {system_info['cpu']['percent']:.1f}%")
        
        if system_info['memory']['percent'] > self.config['memory_threshold']:
            alerts.append(f"内存使用率过高: {system_info['memory']['percent']:.1f}%")
        
        if system_info['disk']['percent'] > self.config['disk_threshold']:
            alerts.append(f"磁盘使用率过高: {system_info['disk']['percent']:.1f}%")
        
        # 检查单个 Python 进程内存使用
        for proc in system_info['python_processes']:
            if proc['memory_mb'] > 2048:  # 超过 2GB
                alerts.append(f"Python 进程 {proc['pid']} 内存使用过高: {proc['memory_mb']:.1f}MB")
        
        return alerts
    
    def log_alerts(self, alerts):
        """记录告警"""
        if not alerts:
            return
        
        alert_log_file = self.config['alert_file']
        os.makedirs(os.path.dirname(alert_log_file), exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        with open(alert_log_file, 'a', encoding='utf-8') as f:
            for alert in alerts:
                f.write(f"[{timestamp}] ALERT: {alert}\n")
                self.logger.warning(f"ALERT: {alert}")
    
    def display_status(self, system_info):
        """显示系统状态"""
        print(f"\n{'='*60}")
        print(f"系统监控 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*60}")
        
        print(f"🖥️  CPU: {system_info['cpu']['percent']:.1f}% ({system_info['cpu']['count']} 核)")
        print(f"💾 内存: {system_info['memory']['percent']:.1f}% "
              f"({system_info['memory']['used_gb']:.1f}GB / {system_info['memory']['total_gb']:.1f}GB)")
        print(f"💿 磁盘: {system_info['disk']['percent']:.1f}% "
              f"(剩余 {system_info['disk']['free_gb']:.1f}GB / 总计 {system_info['disk']['total_gb']:.1f}GB)")
        
        if system_info['python_processes']:
            print(f"\n🐍 Python 进程 ({len(system_info['python_processes'])} 个):")
            for proc in system_info['python_processes']:
                print(f"   PID {proc['pid']}: {proc['memory_mb']:.1f}MB, CPU {proc['cpu_percent']:.1f}%")
        else:
            print("\n🐍 Python 进程: 无")
    
    def run_continuous(self):
        """持续监控模式"""
        self.logger.info("开始系统监控...")
        
        try:
            while True:
                system_info = self.get_system_info()
                if system_info:
                    self.display_status(system_info)
                    
                    alerts = self.check_thresholds(system_info)
                    if alerts:
                        self.log_alerts(alerts)
                
                time.sleep(self.config['check_interval'])
                
        except KeyboardInterrupt:
            self.logger.info("监控已停止")
        except Exception as e:
            self.logger.error(f"监控过程中出错: {e}")
    
    def run_once(self):
        """单次检查模式"""
        system_info = self.get_system_info()
        if system_info:
            self.display_status(system_info)
            
            alerts = self.check_thresholds(system_info)
            if alerts:
                print(f"\n⚠️  发现 {len(alerts)} 个告警:")
                for alert in alerts:
                    print(f"   • {alert}")
                self.log_alerts(alerts)
            else:
                print("\n✅ 系统状态正常")

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "--continuous":
        print("🚀 启动持续监控模式...")
        print("按 Ctrl+C 停止监控")
        monitor = SystemMonitor()
        monitor.run_continuous()
    else:
        print("🔍 执行单次系统检查...")
        monitor = SystemMonitor()
        monitor.run_once()
        print(f"\n💡 提示: 使用 'python {sys.argv[0]} --continuous' 启动持续监控")

if __name__ == "__main__":
    main()
