#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import gzip
import time
import csv
from datetime import datetime
import re
import sys
import os
try:
    # 尝试相对导入（当作为模块导入时）
    from .enums import Permissions, Status, Source, DeleteStatus
except ImportError:
    # 直接导入（当直接运行脚本时）
    from enums import Permissions, Status, Source, DeleteStatus

# 配置参数
XML_FILE = 'discogs_20250601_masters.xml.gz'
TARGET_IDS = ['8517', '10362', '45799']  # 指定要处理的ID
OUTPUT_CSV = 'specific_masters.csv'  # CSV输出文件

# CSV字段定义
CSV_FIELDS = [
    'id', 'y_id', 'key_release', 'notes', 'created_at'
]

# 输出文件路径
OUTPUT_FILE = 'process_output.txt'


# 确保输出文件不存在
if os.path.exists(OUTPUT_FILE):
    os.remove(OUTPUT_FILE)

def write_output(message, print_to_console=True):
    """将消息写入输出文件，并可选择是否打印到控制台"""
    with open(OUTPUT_FILE, 'a', encoding='utf-8') as f:
        f.write(message + '\n')

    if print_to_console:
        print(message)

# MongoDB连接函数已移除，因为不需要入库

def extract_field(content, field_name):
    """从XML内容中提取指定字段的值"""
    pattern = f'<{field_name}>(.*?)</{field_name}>'
    match = re.search(pattern, content, re.DOTALL)
    return match.group(1) if match else None

def extract_master_id(content):
    """从master标签中提取ID属性"""
    pattern = r'<master[^>]+id="([^"]+)"'
    match = re.search(pattern, content)
    return match.group(1) if match else None

def extract_notes(content):
    """提取notes字段"""
    notes = extract_field(content, 'notes')
    return notes.strip() if notes else None

# 数据库查询函数已移除，因为不需要入库

def process_master_content(buffer, sequential_id):
    """处理单个master标签的内容"""
    # 提取ID（从master标签的id属性中）
    master_id = extract_master_id(buffer)
    if not master_id:
        return None

    # 创建master文档（简化版，只包含CSV需要的字段）
    master_doc = {
        'id': master_id,
        'y_id': f"YM{sequential_id}",
        'key_release': extract_field(buffer, 'main_release'),  # main_release保存为key_release
        'notes': extract_notes(buffer),
        'created_at': datetime.now().isoformat(),
    }

    return master_doc

def process_masters():
    """处理XML文件中的master记录"""
    start_time = time.time()

    # 初始化CSV文件
    csv_data = []

    processed_count = 0
    notes_count = 0
    total_records_found = 0
    found_target_ids = []

    try:
        # 打开gzip压缩文件并逐行读取
        with gzip.open(XML_FILE, 'rt', encoding='utf-8') as f:
            buffer = ""
            in_master = False

            for line in f:
                if '<master' in line and 'id=' in line:
                    buffer = line
                    in_master = True
                    total_records_found += 1


                    # 检查是否是单行master标签（自闭合）
                    if '</master>' in line:
                        in_master = False
                        # 提取ID并检查是否在目标ID列表中
                        master_id = extract_master_id(buffer)
                        if master_id and master_id in TARGET_IDS:
                            # 处理master内容，使用连续的序号作为y_id
                            sequential_id = processed_count + 1
                            master_doc = process_master_content(buffer, sequential_id)
                            if master_doc:
                                # 添加到CSV数据列表
                                csv_row = [
                                    master_doc['id'],
                                    master_doc['y_id'],
                                    master_doc['key_release'],
                                    master_doc['notes'],
                                    master_doc['created_at']
                                ]
                                csv_data.append(csv_row)
                                found_target_ids.append(master_id)

                                processed_count += 1
                                if master_doc['notes']:
                                    notes_count += 1

                                # 显示进度
                                log_message = (f"找到目标记录 {processed_count}: y_id={master_doc['y_id']}, "
                                          f"id={master_doc['id']}")
                                write_output(log_message, False)
                                print(log_message)
                        # 清空缓冲区
                        buffer = ""
                elif '</master>' in line and in_master:
                    buffer += line
                    in_master = False

                    # 提取ID并检查是否在目标ID列表中
                    master_id = extract_master_id(buffer)
                    if master_id and master_id in TARGET_IDS:
                        # 处理master内容，使用连续的序号作为y_id
                        sequential_id = processed_count + 1
                        master_doc = process_master_content(buffer, sequential_id)
                        if master_doc:
                            # 添加到CSV数据列表
                            csv_row = [
                                master_doc['id'],
                                master_doc['y_id'],
                                master_doc['key_release'],
                                master_doc['notes'],
                                master_doc['created_at']
                            ]
                            csv_data.append(csv_row)
                            found_target_ids.append(master_id)

                            processed_count += 1
                            if master_doc['notes']:
                                notes_count += 1

                            # 显示进度
                            log_message = (f"找到目标记录 {processed_count}: y_id={master_doc['y_id']}, "
                                      f"id={master_doc['id']}")
                            write_output(log_message, False)
                            print(log_message)

                            # 注释掉提前退出逻辑，确保扫描整个文件
                            # if len(set(found_target_ids)) >= len(TARGET_IDS):
                            #     print("已找到所有目标ID，提前退出")
                            #     break

                    # 清空缓冲区
                    buffer = ""
                elif in_master:
                    buffer += line
    except Exception as e:
        error_msg = f"处理过程中出错: {e}"
        write_output(error_msg)
    finally:
        # 写入CSV文件
        if csv_data:
            with open(OUTPUT_CSV, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                # 写入表头
                writer.writerow(CSV_FIELDS)
                # 写入数据
                writer.writerows(csv_data)
            print(f"CSV文件已生成: {OUTPUT_CSV}")
        else:
            print("未找到任何目标ID的数据")

        # 计算处理时间
        processing_time = time.time() - start_time

        # 输出处理结果统计
        stats = [
            "\n" + "="*50,
            "处理结果统计",
            "="*50,
            f"目标ID: {TARGET_IDS}",
            f"找到的ID: {found_target_ids}",
            f"共处理了 {processed_count} 条目标记录",
            f"XML文件中共扫描 {total_records_found} 条记录",
            f"处理时长: {processing_time:.2f} 秒",
            "="*50
        ]

        # 只打印到控制台，不写入文件
        for stat in stats:
            print(stat)

        # 显示输出文件内容
        print(f"\n详细输出已保存到: {OUTPUT_FILE}")
        if csv_data:
            print(f"CSV数据已保存到: {OUTPUT_CSV}")
            print(f"共写入 {len(csv_data)} 条记录")

# 确保只在直接运行脚本时执行process_masters函数
if __name__ == "__main__":
    process_masters()
