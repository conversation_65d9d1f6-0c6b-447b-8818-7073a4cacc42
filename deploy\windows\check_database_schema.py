#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查数据库中ID字段的实际类型
"""

import os
from pymongo import MongoClient

def connect_to_mongodb():
    """连接到MongoDB"""
    try:
        MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
        DB_NAME = os.getenv('DB_NAME', 'music_test')
        
        client = MongoClient(MONGO_URI)
        client.admin.command('ping')
        return client, client[DB_NAME]
    except Exception as e:
        print(f"❌ MongoDB连接失败: {e}")
        return None, None

def check_id_field_types():
    """检查数据库中ID字段的类型"""
    client, db = connect_to_mongodb()
    if client is None or db is None:
        return
    
    try:
        collection = db['release_new']
        
        print("=" * 80)
        print("📊 RELEASE_NEW 数据库ID字段类型分析")
        print("=" * 80)
        print()
        
        # 获取前10条记录检查ID类型
        print("🔍 检查前10条记录的ID字段类型:")
        records = list(collection.find({}).limit(10))
        
        for i, record in enumerate(records, 1):
            id_value = record.get('id')
            id_type = type(id_value).__name__
            print(f"  {i:2d}. ID值: {id_value} | 类型: {id_type}")
        
        print()
        
        # 统计不同类型的ID数量
        print("📈 ID字段类型统计:")
        
        # 检查字符串类型的ID
        string_count = collection.count_documents({'id': {'$type': 'string'}})
        print(f"  • 字符串类型ID: {string_count:,} 条")
        
        # 检查整数类型的ID
        int_count = collection.count_documents({'id': {'$type': 'int'}})
        print(f"  • 整数类型ID: {int_count:,} 条")
        
        # 检查长整数类型的ID
        long_count = collection.count_documents({'id': {'$type': 'long'}})
        print(f"  • 长整数类型ID: {long_count:,} 条")
        
        # 检查双精度类型的ID
        double_count = collection.count_documents({'id': {'$type': 'double'}})
        print(f"  • 双精度类型ID: {double_count:,} 条")
        
        total_records = collection.count_documents({})
        print(f"  • 总记录数: {total_records:,} 条")
        
        print()
        
        # 测试不同类型的查询
        print("🧪 测试不同类型的ID查询:")
        
        # 获取第一条记录的ID进行测试
        first_record = collection.find_one({})
        if first_record:
            test_id = first_record['id']
            print(f"  测试ID: {test_id} (类型: {type(test_id).__name__})")
            
            # 测试字符串查询
            str_result = collection.find_one({'id': str(test_id)})
            print(f"  • 字符串查询 {'id': str({test_id})}: {'✅ 找到' if str_result else '❌ 未找到'}")
            
            # 测试整数查询
            try:
                int_result = collection.find_one({'id': int(test_id)})
                print(f"  • 整数查询 {'id': int({test_id})}: {'✅ 找到' if int_result else '❌ 未找到'}")
            except:
                print(f"  • 整数查询: ❌ 转换失败")
            
            # 测试原始类型查询
            orig_result = collection.find_one({'id': test_id})
            print(f"  • 原始类型查询 {'id': {test_id}}: {'✅ 找到' if orig_result else '❌ 未找到'}")
        
        print()
        
        # 检查是否有重复ID
        print("🔍 检查重复ID:")
        pipeline = [
            {'$group': {'_id': '$id', 'count': {'$sum': 1}}},
            {'$match': {'count': {'$gt': 1}}},
            {'$sort': {'count': -1}},
            {'$limit': 10}
        ]
        
        duplicates = list(collection.aggregate(pipeline))
        if duplicates:
            print(f"  ⚠️ 发现 {len(duplicates)} 个重复ID:")
            for dup in duplicates:
                print(f"    • ID: {dup['_id']} | 重复次数: {dup['count']}")
        else:
            print("  ✅ 未发现重复ID")
        
        print()
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 检查过程中出错: {e}")
    finally:
        if client:
            client.close()

if __name__ == "__main__":
    check_id_field_types()
