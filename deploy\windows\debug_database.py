#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试数据库中的ID情况
"""

import os
from pymongo import MongoClient

def main():
    try:
        MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
        DB_NAME = os.getenv('DB_NAME', 'music_test')
        
        client = MongoClient(MONGO_URI)
        db = client[DB_NAME]
        collection = db['release_new']
        
        print("🔍 数据库调试信息:")
        print(f"总记录数: {collection.count_documents({}):,}")
        
        # 获取前5个文档
        docs = list(collection.find({}, {'id': 1}).limit(5))
        print(f"前5个ID:")
        for doc in docs:
            print(f"  - {doc['id']}")
        
        # 检查ID 1是否存在
        doc1 = collection.find_one({'id': '1'})
        print(f"ID '1' 存在: {doc1 is not None}")
        
        # 检查ID 1（数字）是否存在
        doc1_int = collection.find_one({'id': 1})
        print(f"ID 1 (数字) 存在: {doc1_int is not None}")
        
        # 获取最小和最大ID
        min_doc = collection.find({}, {'id': 1}).sort('id', 1).limit(1)
        max_doc = collection.find({}, {'id': 1}).sort('id', -1).limit(1)
        
        min_id = list(min_doc)[0]['id'] if min_doc else None
        max_id = list(max_doc)[0]['id'] if max_doc else None
        
        print(f"最小ID: {min_id}")
        print(f"最大ID: {max_id}")
        
        client.close()
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    main()
