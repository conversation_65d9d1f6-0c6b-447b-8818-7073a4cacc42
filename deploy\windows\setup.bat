@echo off
chcp 936 >nul
echo ========================================
echo Discogs Data Processing Tool - Windows Setup Script
echo ========================================

:: Set script directory as current working directory
cd /d "%~dp0"

:: Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not found, please install Python 3.8 or higher
    echo Download from: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo [INFO] Detected Python version:
python --version

:: Create virtual environment
echo.
echo [SETUP] Creating Python virtual environment...
if not exist "venv" (
    python -m venv venv
    if errorlevel 1 (
        echo [ERROR] Failed to create virtual environment
        pause
        exit /b 1
    )
    echo [OK] Virtual environment created successfully
) else (
    echo [INFO] Virtual environment already exists, skipping creation
)

:: Activate virtual environment
echo.
echo [SETUP] Activating virtual environment...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo [ERROR] Failed to activate virtual environment
    pause
    exit /b 1
)

:: Upgrade pip
echo.
echo [SETUP] Upgrading pip...
python -m pip install --upgrade pip

:: Install dependencies
echo.
echo [SETUP] Installing project dependencies...
if exist "..\..\requirements.txt" (
    echo [INFO] Installing from requirements.txt...
    pip install -r "..\..\requirements.txt"
) else (
    echo [WARNING] requirements.txt not found, installing core dependencies manually
    pip install pymongo flask flask-cors psutil
)

if errorlevel 1 (
    echo [ERROR] Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo ========================================
echo [SUCCESS] Setup completed successfully!
echo ========================================
echo.
echo Usage instructions:
echo 1. Place gzip data files in this directory or data subdirectory
echo 2. Modify configuration files (config_*.env) as needed
echo 3. Run manage_all.bat to start processing modules
echo 4. Or run start_all_quick.bat to start all modules at once
echo.
echo Available commands:
echo - manage_all.bat: Interactive module management
echo - start_all_quick.bat: Quick start all modules
echo - run_module.bat [module]: Start specific module
echo.
pause
