@echo off
chcp 65001 >nul
echo ========================================
echo Discogs Artists 处理工具 - 打包脚本
echo ========================================

:: 设置脚本目录为当前工作目录
cd /d "%~dp0"

:: 创建打包目录
set PACKAGE_DIR=discogs-artists-windows
set PACKAGE_ZIP=%PACKAGE_DIR%.zip

echo 正在创建打包目录...
if exist "%PACKAGE_DIR%" (
    rmdir /s /q "%PACKAGE_DIR%"
)
mkdir "%PACKAGE_DIR%"

:: 复制核心文件
echo 正在复制核心文件...
copy "..\..\artists\process_artists.py" "%PACKAGE_DIR%\" >nul
copy "..\..\artists\enums.py" "%PACKAGE_DIR%\enums_artists.py" >nul
copy "..\..\label\process_labels.py" "%PACKAGE_DIR%\" >nul
copy "..\..\label\enums.py" "%PACKAGE_DIR%\enums_labels.py" >nul
copy "..\..\master\process_master.py" "%PACKAGE_DIR%\" >nul
copy "..\..\master\enums.py" "%PACKAGE_DIR%\enums_masters.py" >nul
copy "..\..\release\process_releases.py" "%PACKAGE_DIR%\" >nul
copy "..\..\release\enums.py" "%PACKAGE_DIR%\enums_releases.py" >nul
copy "..\..\requirements.txt" "%PACKAGE_DIR%\" >nul

:: 复制部署文件
echo 正在复制部署文件...
copy "setup.bat" "%PACKAGE_DIR%\" >nul
copy "run_module.bat" "%PACKAGE_DIR%\" >nul
copy "manage_all.bat" "%PACKAGE_DIR%\" >nul
copy "config_*.env" "%PACKAGE_DIR%\" >nul
copy "test_gzip.py" "%PACKAGE_DIR%\" >nul
copy "monitor.py" "%PACKAGE_DIR%\" >nul
copy "install_service.bat" "%PACKAGE_DIR%\" >nul
copy "README.md" "%PACKAGE_DIR%\" >nul

:: 创建示例数据目录
mkdir "%PACKAGE_DIR%\data"
echo # 请将 gzip 数据文件放置在此目录下 > "%PACKAGE_DIR%\data\README.txt"

:: 创建日志目录
mkdir "%PACKAGE_DIR%\logs"
echo # 处理日志将保存在此目录下 > "%PACKAGE_DIR%\logs\README.txt"

:: 创建快速开始脚本
echo 正在创建快速开始脚本...
(
echo @echo off
echo chcp 65001 ^>nul
echo echo ========================================
echo echo Discogs Artists 处理工具 - 快速开始
echo echo ========================================
echo echo.
echo echo 1. 首次使用请先运行: setup.bat
echo echo 2. 配置参数请编辑: config.env
echo echo 3. 测试 gzip 文件: python test_gzip.py [文件名]
echo echo 4. 开始处理数据: run.bat
echo echo.
echo echo 详细说明请查看: README.md
echo echo.
echo pause
) > "%PACKAGE_DIR%\快速开始.bat"

:: 创建压缩包（如果有 PowerShell）
echo 正在创建压缩包...
powershell -command "Compress-Archive -Path '%PACKAGE_DIR%' -DestinationPath '%PACKAGE_ZIP%' -Force" 2>nul
if exist "%PACKAGE_ZIP%" (
    echo ✅ 压缩包创建成功: %PACKAGE_ZIP%
) else (
    echo ⚠️  压缩包创建失败，请手动压缩 %PACKAGE_DIR% 目录
)

echo.
echo ========================================
echo 打包完成！
echo ========================================
echo.
echo 打包内容:
echo - %PACKAGE_DIR%\          (完整部署包)
if exist "%PACKAGE_ZIP%" (
    echo - %PACKAGE_ZIP%         (压缩包)
)
echo.
echo 部署说明:
echo 1. 将打包文件复制到 Windows 服务器
echo 2. 解压并进入目录
echo 3. 将 gzip 数据文件放入 data 目录
echo 4. 运行 setup.bat 初始化环境
echo 5. 编辑 config_*.env 配置各模块参数
echo 6. 运行 manage_all.bat 管理所有模块
echo 7. 或使用 run_module.bat [模块名] 单独运行
echo.
pause
