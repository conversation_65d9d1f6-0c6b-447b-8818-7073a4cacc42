@echo off
chcp 65001 >nul
echo ========================================
echo Discogs Artists 处理工具 - Windows 服务安装
echo ========================================

:: 检查管理员权限
net session >nul 2>&1
if errorlevel 1 (
    echo 错误: 需要管理员权限才能安装 Windows 服务
    echo 请右键点击此脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)

:: 设置脚本目录为当前工作目录
cd /d "%~dp0"

:: 检查是否已安装 Python Windows 服务支持
pip show pywin32 >nul 2>&1
if errorlevel 1 (
    echo 正在安装 Windows 服务支持...
    call venv\Scripts\activate.bat
    pip install pywin32
    if errorlevel 1 (
        echo 错误: 安装 pywin32 失败
        pause
        exit /b 1
    )
)

:: 创建服务脚本
echo 正在创建服务脚本...

:: 这里可以添加具体的 Windows 服务安装逻辑
echo 注意: Windows 服务安装需要额外的服务包装脚本
echo 建议使用 NSSM (Non-Sucking Service Manager) 来包装 Python 脚本为服务
echo.
echo 下载 NSSM: https://nssm.cc/download
echo.
echo 使用 NSSM 安装服务的命令示例:
echo nssm install "DiscogsArtistsProcessor" "%CD%\run.bat"
echo nssm set "DiscogsArtistsProcessor" AppDirectory "%CD%"
echo nssm start "DiscogsArtistsProcessor"
echo.

pause
