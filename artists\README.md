# Discogs Artists 数据处理模块

## 概述

本模块用于处理 Discogs 艺术家(Artists)数据的 XML 文件，将其解析并存储到 MongoDB 数据库中。该模块完全参照`label/process_labels.py`的处理模式实现。

## 文件结构

```
artists/
├── README.md                           # 本说明文件
├── process_artists.py                  # 主处理脚本
├── enums.py                           # 枚举定义
├── discogs_20250501_artists.xml       # XML数据源文件
└── process_output.txt                 # 处理日志输出（运行后生成）
```

## 功能特性

### 支持的数据字段

- **基础信息**：

  - `id` - 艺术家 ID
  - `name` (n) - 艺术家名称
  - `realname` - 真实姓名
  - `profile` - 简介描述

- **关联信息**：

  - `variations` - 名称变体列表
  - `aliases` - 别名列表（包含 id 和 name）
  - `groups` - 组合/团体列表（包含 id 和 name）
  - `members` - 成员列表（包含 id 和 name）

- **系统字段**：
  - `y_id` - 系统 ID（格式：YA1、YA2、YA3...，按处理顺序连续编号）
  - `delete_status` - 逻辑删除状态
  - `created_at` / `updated_at` - 时间戳
  - `source` - 数据来源（DISCOGS）
  - `permissions` - 权限设置
  - `status` - 状态

## 配置参数

在 `process_artists.py` 中可以修改以下配置：

```python
XML_FILE = 'discogs_20250501_artists.xml'              # XML源文件
MONGO_URI = '**********************************************************'  # MongoDB连接
DB_NAME = 'music_test'                                  # 数据库名
MAX_RECORDS = 1000                                      # 最大处理记录数
OUTPUT_FILE = 'process_output.txt'                     # 输出日志文件
```

## 使用方法

### 1. 环境准备

确保已安装必要的 Python 包：

```bash
pip install pymongo
```

### 2. 数据准备

将 Discogs 艺术家 XML 文件放置在 `artists/` 目录下，并确保文件名与配置中的 `XML_FILE` 一致。

### 3. 运行处理脚本

```bash
cd discogs/artists
python3 process_artists.py
```

### 4. 查看结果

- 控制台会显示处理进度和统计信息
- 详细日志保存在 `process_output.txt` 文件中
- 处理后的数据存储在 MongoDB 的 `artists_new` 集合中

## 输出示例

```
已处理 10 条记录...
已处理 20 条记录...
...
==================================================
处理结果统计
==================================================
共处理了 1000 条记录
提取了 12345 个variations
提取了 15234 个aliases
提取了 8765 个groups
提取了 4321 个members
XML文件中共发现 50000 条记录
由于设置了最大处理记录数限制 (MAX_RECORDS=1000)，只处理了部分记录
处理时长: 45.67 秒
平均每条记录处理时间: 0.0457 秒
==================================================

详细输出已保存到: process_output.txt
```

## 数据库结构

处理后的数据存储在 MongoDB 的 `artists_new` 集合中，文档结构示例：

```json
{
  "_id": ObjectId("..."),
  "id": "3",
  "y_id": "YA1",
  "name": "Josh Wink",
  "realname": "Joshua Winkelman",
  "profile": "Electronic music DJ, label owner...",
  "variations": ["J Wink", "J.Wink", "JoshWink"],
  "aliases": [
    {"id": "11217", "name": "Size 9"},
    {"id": "95949", "name": "The Crusher"}
  ],
  "groups": [
    {"id": "34803", "name": "E-Culture"},
    {"id": "55692", "name": "Abundance Of Cups"}
  ],
  "members": [
    {"id": "26", "name": "Alexi Delano"},
    {"id": "27", "name": "Cari Lekebusch"}
  ],
  "delete_status": 0,
  "deleted_at": null,
  "created_at": ISODate("2025-01-26T..."),
  "updated_at": ISODate("2025-01-26T..."),
  "source": 1,
  "permissions": 1,
  "status": 1
}
```

## 错误处理

- 脚本包含完善的错误处理机制
- 解析错误不会中断整个处理流程
- 所有错误信息都会记录到输出文件中
- 支持从中断点继续处理（通过保留原始\_id）

## 注意事项

1. **内存使用**：脚本逐行读取 XML 文件，内存占用较低
2. **处理时间**：根据 XML 文件大小和 MAX_RECORDS 设置而定
3. **数据完整性**：处理前会清空 `artists_new` 集合
4. **ID 保留**：如果原 `artists` 集合中存在相同记录，会保留原始\_id

## 故障排除

### 常见问题

1. **MongoDB 连接失败**

   - 检查 MONGO_URI 配置
   - 确认 MongoDB 服务运行状态
   - 验证网络连接和认证信息

2. **XML 文件读取错误**

   - 确认文件路径和名称正确
   - 检查文件编码（应为 UTF-8）
   - 验证文件权限

3. **内存不足**
   - 减少 MAX_RECORDS 值
   - 检查系统可用内存

### 日志分析

查看 `process_output.txt` 文件获取详细的处理日志和错误信息。

## 扩展功能

如需添加新的字段处理或修改现有逻辑，请参考现有的字段提取函数模式：

```python
def extract_new_field(content):
    """提取新字段"""
    # 实现提取逻辑
    return extracted_data
```

然后在 `process_artist_content()` 函数中添加对应的字段。
