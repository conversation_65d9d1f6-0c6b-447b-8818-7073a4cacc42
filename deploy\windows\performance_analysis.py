#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
性能分析脚本：对比增量插入 vs 清空重新插入 vs 多线程处理
"""

import os
import time
from pymongo import MongoClient

def analyze_current_situation():
    """分析当前数据情况"""
    print("="*60)
    print("📊 当前数据情况分析")
    print("="*60)
    
    try:
        MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
        DB_NAME = os.getenv('DB_NAME', 'music_test')
        
        client = MongoClient(MONGO_URI)
        db = client[DB_NAME]
        collection = db['release_new']
        
        # 基本数据统计
        current_count = collection.count_documents({})
        xml_total = 18333401
        missing_count = xml_total - current_count
        
        print(f"📈 数据统计:")
        print(f"  • XML文件总记录数: {xml_total:,}")
        print(f"  • 数据库现有记录: {current_count:,}")
        print(f"  • 缺失记录数: {missing_count:,}")
        print(f"  • 完成度: {current_count/xml_total*100:.1f}%")
        
        client.close()
        return current_count, xml_total, missing_count
        
    except Exception as e:
        print(f"❌ 数据分析失败: {e}")
        return 0, 18333401, 18333401

def test_query_performance():
    """测试查询性能"""
    print("\n🔍 查询性能测试...")
    
    try:
        MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
        DB_NAME = os.getenv('DB_NAME', 'music_test')
        
        client = MongoClient(MONGO_URI)
        db = client[DB_NAME]
        collection = db['release_new']
        
        # 测试100次查询的平均时间
        test_ids = list(range(1, 101))
        start_time = time.time()
        
        for test_id in test_ids:
            collection.find_one({'id': test_id}, {'_id': 1})
        
        total_time = time.time() - start_time
        avg_query_time = total_time / len(test_ids)
        
        print(f"  • 100次查询总时间: {total_time:.3f}秒")
        print(f"  • 单次查询平均时间: {avg_query_time*1000:.2f}ms")
        print(f"  • 每秒可查询: {1/avg_query_time:.0f}次")
        
        client.close()
        return avg_query_time
        
    except Exception as e:
        print(f"❌ 查询性能测试失败: {e}")
        return 0.01  # 假设10ms

def test_insert_performance():
    """测试插入性能"""
    print("\n📝 插入性能测试...")
    
    try:
        MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
        DB_NAME = os.getenv('DB_NAME', 'music_test')
        
        client = MongoClient(MONGO_URI)
        db = client[DB_NAME]
        collection = db['release_new']
        
        # 测试10次插入的平均时间
        test_docs = []
        for i in range(10):
            test_docs.append({
                'id': 99999990 + i,
                'title': f'Test Document {i}',
                'y_id': f'TEST{i}',
                'artists': [],
                'labels': [],
                'formats': []
            })
        
        start_time = time.time()
        for doc in test_docs:
            collection.insert_one(doc)
        total_time = time.time() - start_time
        
        # 清理测试数据
        collection.delete_many({'id': {'$gte': 99999990, '$lt': 100000000}})
        
        avg_insert_time = total_time / len(test_docs)
        
        print(f"  • 10次插入总时间: {total_time:.3f}秒")
        print(f"  • 单次插入平均时间: {avg_insert_time*1000:.2f}ms")
        print(f"  • 每秒可插入: {1/avg_insert_time:.0f}次")
        
        client.close()
        return avg_insert_time
        
    except Exception as e:
        print(f"❌ 插入性能测试失败: {e}")
        return 0.05  # 假设50ms

def calculate_processing_times(current_count, xml_total, missing_count, query_time, insert_time):
    """计算各种处理方式的时间"""
    print("\n⏱️ 处理时间计算...")
    
    # 方案1: 增量插入（当前方式）
    incremental_queries = xml_total  # 需要查询所有记录
    incremental_inserts = missing_count  # 只插入缺失记录
    incremental_time = (incremental_queries * query_time + incremental_inserts * insert_time) / 3600
    
    # 方案2: 清空重新插入
    clear_time = 60  # 假设清空需要1分钟
    full_insert_time = (xml_total * insert_time) / 3600
    clear_and_insert_time = clear_time/3600 + full_insert_time
    
    # 方案3: 多线程增量插入（4线程）
    thread_count = 4
    multithread_time = incremental_time / thread_count * 1.2  # 考虑线程开销
    
    # 方案4: 多线程全量插入（4线程）
    multithread_full_time = clear_and_insert_time / thread_count * 1.2
    
    print(f"📊 处理时间对比:")
    print(f"  1️⃣ 单线程增量插入: {incremental_time:.1f}小时")
    print(f"     - 查询时间: {incremental_queries * query_time / 3600:.1f}小时")
    print(f"     - 插入时间: {incremental_inserts * insert_time / 3600:.1f}小时")
    print(f"  2️⃣ 单线程清空重插: {clear_and_insert_time:.1f}小时")
    print(f"     - 清空时间: {clear_time/60:.1f}分钟")
    print(f"     - 插入时间: {full_insert_time:.1f}小时")
    print(f"  3️⃣ 多线程增量插入: {multithread_time:.1f}小时")
    print(f"  4️⃣ 多线程清空重插: {multithread_full_time:.1f}小时")
    
    return {
        'incremental': incremental_time,
        'clear_insert': clear_and_insert_time,
        'multithread_incremental': multithread_time,
        'multithread_full': multithread_full_time
    }

def analyze_pros_cons():
    """分析各方案优缺点"""
    print("\n📋 方案优缺点分析:")
    print("="*60)
    
    print("1️⃣ 单线程增量插入:")
    print("   ✅ 优点: 保留现有数据，安全可靠，支持断点续传")
    print("   ❌ 缺点: 需要大量查询操作，速度最慢")
    print("   🎯 适用: 数据安全性要求高，不急于完成")
    
    print("\n2️⃣ 单线程清空重插:")
    print("   ✅ 优点: 无需查询，插入速度快，数据一致性好")
    print("   ❌ 缺点: 丢失现有数据，无法断点续传，风险较高")
    print("   🎯 适用: 可以接受重新开始，追求处理速度")
    
    print("\n3️⃣ 多线程增量插入:")
    print("   ✅ 优点: 保留现有数据，速度比单线程快")
    print("   ❌ 缺点: 实现复杂，数据库连接压力大，可能有并发问题")
    print("   🎯 适用: 技术实现能力强，数据库性能好")
    
    print("\n4️⃣ 多线程清空重插:")
    print("   ✅ 优点: 速度最快，实现相对简单")
    print("   ❌ 缺点: 丢失现有数据，数据库连接压力大")
    print("   🎯 适用: 追求最快速度，可接受数据重置")

def recommend_solution(times, current_count, xml_total):
    """推荐最佳方案"""
    print("\n🎯 推荐方案:")
    print("="*60)
    
    completion_rate = current_count / xml_total
    
    if completion_rate < 0.5:
        # 完成度低于50%，推荐清空重插
        if times['multithread_full'] < times['incremental'] * 0.3:
            print("🚀 推荐: 多线程清空重插")
            print("   理由: 当前完成度较低，重新开始更高效")
        else:
            print("🚀 推荐: 单线程清空重插")
            print("   理由: 当前完成度较低，重新开始更高效且实现简单")
    else:
        # 完成度高于50%，推荐增量插入
        if times['multithread_incremental'] < times['clear_insert'] * 0.7:
            print("🚀 推荐: 多线程增量插入")
            print("   理由: 当前完成度较高，保留现有数据更划算")
        else:
            print("🚀 推荐: 单线程增量插入")
            print("   理由: 当前完成度较高，保留现有数据，实现简单")
    
    print(f"\n📊 决策依据:")
    print(f"   • 当前完成度: {completion_rate*100:.1f}%")
    print(f"   • 最快方案: {min(times, key=times.get)} ({min(times.values()):.1f}小时)")
    print(f"   • 最安全方案: 单线程增量插入 ({times['incremental']:.1f}小时)")

def main():
    """主函数"""
    print("🚀 RELEASES 处理性能分析工具")
    
    # 1. 分析当前情况
    current_count, xml_total, missing_count = analyze_current_situation()
    
    # 2. 测试性能
    query_time = test_query_performance()
    insert_time = test_insert_performance()
    
    # 3. 计算处理时间
    times = calculate_processing_times(current_count, xml_total, missing_count, query_time, insert_time)
    
    # 4. 分析优缺点
    analyze_pros_cons()
    
    # 5. 推荐方案
    recommend_solution(times, current_count, xml_total)
    
    print(f"\n{'='*60}")
    print("分析完成！请根据推荐方案选择最适合的处理方式。")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
