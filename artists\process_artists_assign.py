#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import csv
import json
from datetime import datetime
import re
import sys
import os
import gzip
try:
    # 尝试相对导入（当作为模块导入时）
    from .enums import Permissions, Status, Source, DeleteStatus
except ImportError:
    # 直接导入（当直接运行脚本时）
    from enums import Permissions, Status, Source, DeleteStatus

# 配置参数
XML_FILE = 'discogs_20250601_artists.xml.gz'
TARGET_IDS = ['15885', '45467', '82730']  # 目标ID列表
CSV_OUTPUT_FILE = 'specific_artists_output.csv'

# 输出文件路径
OUTPUT_FILE = 'specific_artists_process_output.txt'


# 确保输出文件不存在
if os.path.exists(OUTPUT_FILE):
    os.remove(OUTPUT_FILE)

def write_output(message, print_to_console=True):
    """将消息写入输出文件，并可选择是否打印到控制台"""
    with open(OUTPUT_FILE, 'a', encoding='utf-8') as f:
        f.write(message + '\n')

    if print_to_console:
        print(message)

# 移除了数据库连接函数，因为现在只输出CSV

def extract_field(content, field_name):
    """从XML内容中提取指定字段的值"""
    pattern = f'<{field_name}>(.*?)</{field_name}>'
    match = re.search(pattern, content, re.DOTALL)
    return match.group(1) if match else None


def extract_namevariations(content):
    """提取namevariations字段"""
    namevariations = []
    namevariations_match = re.search(r'<namevariations>(.*?)</namevariations>', content, re.DOTALL)
    if not namevariations_match:
        return namevariations

    namevariations_content = namevariations_match.group(1)
    extracted_variations = re.findall(r'<name>(.*?)</name>', namevariations_content, re.DOTALL)
    # 清理名称变体，去除前后空白字符
    return [variation.strip() for variation in extracted_variations if variation.strip()]

def extract_aliases(content):
    """提取aliases字段"""
    aliases = []
    aliases_match = re.search(r'<aliases>(.*?)</aliases>', content, re.DOTALL)
    if not aliases_match:
        return aliases

    aliases_content = aliases_match.group(1)

    # 使用正则表达式提取aliases
    alias_pattern = r'<name id="?(\d+)"?>(.*?)</name>'
    alias_matches = re.findall(alias_pattern, aliases_content)

    if alias_matches:
        for alias_id, alias_text in alias_matches:
            clean_text = alias_text.strip()
            if clean_text:  # 只添加非空的别名
                aliases.append({
                    'id': alias_id,
                    'name': clean_text
                })
    else:
        # 备用方法：手动解析
        alias_tags = aliases_content.split('</name>')
        for tag in alias_tags:
            if '<name id=' in tag:
                # 提取id
                id_start = tag.find('id="') + 4
                if id_start > 4:  # 确保找到了id="
                    id_end = tag.find('"', id_start)
                    if id_end > id_start:
                        alias_id = tag[id_start:id_end]

                        # 提取文本
                        text_start = tag.find('>', tag.find('<name id=')) + 1
                        if text_start > 0:
                            alias_text = tag[text_start:].strip()
                            aliases.append({
                                'id': alias_id,
                                'name': alias_text
                            })

    return aliases

def extract_groups(content):
    """提取groups字段"""
    groups = []
    groups_match = re.search(r'<groups>(.*?)</groups>', content, re.DOTALL)
    if not groups_match:
        return groups

    groups_content = groups_match.group(1)

    # 使用正则表达式提取groups
    group_pattern = r'<name id="?(\d+)"?>(.*?)</name>'
    group_matches = re.findall(group_pattern, groups_content)

    if group_matches:
        for group_id, group_text in group_matches:
            clean_text = group_text.strip()
            if clean_text:  # 只添加非空的组合名称
                groups.append({
                    'id': group_id,
                    'name': clean_text
                })
    else:
        # 备用方法：手动解析
        group_tags = groups_content.split('</name>')
        for tag in group_tags:
            if '<name id=' in tag:
                # 提取id
                id_start = tag.find('id="') + 4
                if id_start > 4:  # 确保找到了id="
                    id_end = tag.find('"', id_start)
                    if id_end > id_start:
                        group_id = tag[id_start:id_end]

                        # 提取文本
                        text_start = tag.find('>', tag.find('<name id=')) + 1
                        if text_start > 0:
                            group_text = tag[text_start:].strip()
                            groups.append({
                                'id': group_id,
                                'name': group_text
                            })

    return groups

def extract_members(content):
    """提取members字段"""
    members = []
    members_match = re.search(r'<members>(.*?)</members>', content, re.DOTALL)
    if not members_match:
        return members

    members_content = members_match.group(1)

    # 使用正则表达式提取members
    member_pattern = r'<name id="?(\d+)"?>(.*?)</name>'
    member_matches = re.findall(member_pattern, members_content)

    if member_matches:
        for member_id, member_text in member_matches:
            clean_text = member_text.strip()
            if clean_text:  # 只添加非空的成员名称
                members.append({
                    'id': member_id,
                    'name': clean_text
                })
    else:
        # 备用方法：手动解析
        member_tags = members_content.split('</name>')
        for tag in member_tags:
            if '<name id=' in tag:
                # 提取id
                id_start = tag.find('id="') + 4
                if id_start > 4:  # 确保找到了id="
                    id_end = tag.find('"', id_start)
                    if id_end > id_start:
                        member_id = tag[id_start:id_end]

                        # 提取文本
                        text_start = tag.find('>', tag.find('<name id=')) + 1
                        if text_start > 0:
                            member_text = tag[text_start:].strip()
                            members.append({
                                'id': member_id,
                                'name': member_text
                            })

    return members

def extract_sites(content):
    """提取sites字段（从urls标签中获取所有url）"""
    sites = []
    urls_match = re.search(r'<urls>(.*?)</urls>', content, re.DOTALL)
    if not urls_match:
        return sites

    urls_content = urls_match.group(1)

    # 使用正则表达式提取所有url标签的内容
    url_pattern = r'<url>(.*?)</url>'
    url_matches = re.findall(url_pattern, urls_content, re.DOTALL)

    if url_matches:
        for url_text in url_matches:
            # 清理URL，去除前后空白字符并处理HTML实体
            clean_url = url_text.strip()
            if clean_url:  # 只添加非空的URL
                # 处理HTML实体转义
                clean_url = clean_url.replace('&amp;', '&')
                sites.append(clean_url)

    return sites

# 移除了数据库查询函数，因为现在只输出CSV

def process_artist_content(buffer, sequential_id):
    """处理单个artist标签的内容（移除数据库依赖）"""
    # 提取ID
    artist_id = extract_field(buffer, 'id')
    if not artist_id:
        return None

    # 创建artist文档（简化版，用于CSV输出）
    artist_doc = {
        'id': artist_id,
        'y_id': f"YA{sequential_id}",
        'name': extract_field(buffer, 'name'),
        'realname': extract_field(buffer, 'realname'),
        'profile': extract_field(buffer, 'profile'),
        'variations': extract_namevariations(buffer),
        'aliases': extract_aliases(buffer),
        'groups': extract_groups(buffer),
        'members': extract_members(buffer),
        'sites': extract_sites(buffer),
        'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
    }

    return artist_doc

def write_to_csv(artist_docs):
    """将艺术家数据写入CSV文件"""
    if not artist_docs:
        return

    # 定义CSV字段
    fieldnames = [
        'id', 'y_id', 'name', 'realname', 'profile',
        'variations', 'aliases', 'groups', 'members', 'sites', 'created_at'
    ]

    with open(CSV_OUTPUT_FILE, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        # 写入表头
        writer.writeheader()

        # 写入数据
        for artist_doc in artist_docs:
            # 将列表字段转换为JSON数组格式以便CSV存储
            csv_row = artist_doc.copy()

            # 处理variations字段 - 转换为JSON数组
            csv_row['variations'] = json.dumps(artist_doc['variations'], ensure_ascii=False) if artist_doc['variations'] else '[]'

            # 处理aliases字段 - 转换为JSON对象数组
            csv_row['aliases'] = json.dumps(artist_doc['aliases'], ensure_ascii=False) if artist_doc['aliases'] else '[]'

            # 处理groups字段 - 转换为JSON对象数组
            csv_row['groups'] = json.dumps(artist_doc['groups'], ensure_ascii=False) if artist_doc['groups'] else '[]'

            # 处理members字段 - 转换为JSON对象数组
            csv_row['members'] = json.dumps(artist_doc['members'], ensure_ascii=False) if artist_doc['members'] else '[]'

            # 处理sites字段 - 转换为JSON数组
            csv_row['sites'] = json.dumps(artist_doc['sites'], ensure_ascii=False) if artist_doc['sites'] else '[]'

            writer.writerow(csv_row)

def process_artists():
    """处理XML文件中指定ID的artist记录"""
    start_time = time.time()

    processed_count = 0
    found_artists = []
    total_records_scanned = 0
    target_ids_found = set()

    try:
        # 打开gz压缩文件并逐行读取
        with gzip.open(XML_FILE, 'rt', encoding='utf-8') as f:
            buffer = ""
            in_artist = False

            for line in f:
                if '<artist>' in line:
                    buffer = line
                    in_artist = True
                    total_records_scanned += 1
                elif '</artist>' in line and in_artist:
                    buffer += line
                    in_artist = False

                    # 提取ID并检查是否在目标列表中
                    artist_id = extract_field(buffer, 'id')
                    if artist_id and artist_id in TARGET_IDS:
                        # 处理artist内容
                        sequential_id = len(found_artists) + 1
                        artist_doc = process_artist_content(buffer, sequential_id)
                        if artist_doc:
                            found_artists.append(artist_doc)
                            target_ids_found.add(artist_id)
                            processed_count += 1

                            name_short = artist_doc['name'][:50] if artist_doc['name'] else "无名称"
                            log_message = (f"找到目标记录 {processed_count}: y_id={artist_doc['y_id']}, "
                                      f"id={artist_doc['id']}, name={name_short}...")
                            write_output(log_message)

                            # 如果找到了所有目标ID，可以提前退出
                            if len(target_ids_found) == len(TARGET_IDS):
                                write_output("已找到所有目标ID，停止扫描")
                                break

                    # 清空缓冲区
                    buffer = ""
                elif in_artist:
                    buffer += line

                # 每扫描1000条记录显示一次进度
                if total_records_scanned % 1000 == 0:
                    print(f"已扫描 {total_records_scanned} 条记录，找到 {processed_count} 个目标...")
    except Exception as e:
        error_msg = f"处理过程中出错: {e}"
        write_output(error_msg)
    finally:
        # 写入CSV文件
        if found_artists:
            write_to_csv(found_artists)
            write_output(f"数据已写入CSV文件: {CSV_OUTPUT_FILE}")

        # 计算处理时间
        processing_time = time.time() - start_time

        # 输出处理结果统计
        stats = [
            "\n" + "="*50,
            "处理结果统计",
            "="*50,
            f"目标ID列表: {TARGET_IDS}",
            f"找到的ID: {list(target_ids_found)}",
            f"未找到的ID: {list(set(TARGET_IDS) - target_ids_found)}",
            f"共扫描了 {total_records_scanned} 条记录",
            f"成功处理了 {processed_count} 条目标记录",
        ]

        if found_artists:
            variations_count = sum(len(artist['variations']) for artist in found_artists)
            aliases_count = sum(len(artist['aliases']) for artist in found_artists)
            groups_count = sum(len(artist['groups']) for artist in found_artists)
            members_count = sum(len(artist['members']) for artist in found_artists)
            sites_count = sum(len(artist['sites']) for artist in found_artists)

            stats.extend([
                f"提取了 {variations_count} 个variations",
                f"提取了 {aliases_count} 个aliases",
                f"提取了 {groups_count} 个groups",
                f"提取了 {members_count} 个members",
                f"提取了 {sites_count} 个sites",
            ])

        stats.extend([
            f"处理时长: {processing_time:.2f} 秒",
            f"CSV输出文件: {CSV_OUTPUT_FILE}",
            "="*50
        ])

        # 打印到控制台
        for stat in stats:
            print(stat)

        # 显示输出文件内容
        print(f"\n详细输出已保存到: {OUTPUT_FILE}")

# 确保只在直接运行脚本时执行process_artists函数
if __name__ == "__main__":
    process_artists()
