@echo off
chcp 65001 >nul
echo ================================================================
echo 🔄 RELEASES 完整重新处理脚本
echo ================================================================
echo.
echo 此脚本将执行以下操作：
echo 1. 验证当前数据完整性
echo 2. 清理和重置环境（可选）
echo 3. 启动完整的releases处理
echo.
echo ⚠️ 警告: 这将从release ID 1开始重新处理所有数据
echo.

set /p confirm="是否继续？(y/N): "
if /i not "%confirm%"=="y" (
    echo ❌ 操作已取消
    pause
    exit /b 1
)

echo.
echo 🔍 步骤1: 验证当前数据完整性...
echo ================================================================
python verify_releases_integrity.py
if errorlevel 1 (
    echo ❌ 数据完整性验证失败
    pause
    exit /b 1
)

echo.
echo.
set /p reset="是否需要重置环境（清空数据库和进度文件）？(y/N): "
if /i "%reset%"=="y" (
    echo.
    echo 🗑️ 步骤2: 重置环境...
    echo ================================================================
    python reset_releases_environment.py
    if errorlevel 1 (
        echo ❌ 环境重置失败
        pause
        exit /b 1
    )
) else (
    echo ℹ️ 跳过环境重置
)

echo.
echo.
echo 🚀 步骤3: 启动完整的releases处理...
echo ================================================================
echo 处理参数:
echo   • 起始ID: 1
echo   • 目标集合: release_new
echo   • 检查点间隔: 1000条记录
echo   • 错误追踪: 启用
echo.

set /p start="是否开始处理？(y/N): "
if /i not "%start%"=="y" (
    echo ❌ 处理已取消
    pause
    exit /b 1
)

echo.
echo 📝 开始处理，详细日志将保存到 process_output.txt
echo 💡 提示: 可以随时按 Ctrl+C 中断处理，支持断点续传
echo.

REM 设置环境变量确保从ID 1开始
set START_RELEASE_ID=1
set MAX_RECORDS=0
set CHECKPOINT_INTERVAL=1000

REM 启动处理
python process_releases.py

echo.
echo ================================================================
if errorlevel 1 (
    echo ❌ 处理过程中出现错误
    echo 💡 请检查 process_output.txt 和 logs/releases_errors.log
) else (
    echo ✅ 处理完成
    echo 💡 建议运行 verify_releases_integrity.py 验证结果
)
echo ================================================================

pause
