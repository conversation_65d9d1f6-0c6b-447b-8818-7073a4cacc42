@echo off
setlocal enabledelayedexpansion
chcp 936 >nul

:: Check parameters
if "%1"=="" (
    echo [ERROR] Please specify module name
    echo Usage: run_module.bat [artists^|labels^|masters^|releases]
    echo.
    echo Examples:
    echo   run_module.bat artists
    echo   run_module.bat releases
    pause
    exit /b 1
)

set MODULE=%1
set CONFIG_FILE=config_%MODULE%.env

echo ========================================
echo Discogs %MODULE% Processing Tool - Long Running Version
echo ========================================

:: Set script directory as current working directory
cd /d "%~dp0"

:: Check if configuration file exists
if not exist "%CONFIG_FILE%" (
    echo [ERROR] Configuration file not found: %CONFIG_FILE%
    echo Please ensure configuration file exists and is correctly named
    pause
    exit /b 1
)

:: Check if virtual environment exists
if not exist "venv\Scripts\activate.bat" (
    echo [ERROR] Virtual environment does not exist, please run setup.bat first
    pause
    exit /b 1
)

:: Create necessary directories
if not exist "logs" mkdir logs
if not exist "progress" mkdir progress
if not exist "data" mkdir data

:: Load environment configuration
echo [CONFIG] Loading configuration file: %CONFIG_FILE%
for /f "usebackq tokens=1,2 delims==" %%a in ("%CONFIG_FILE%") do (
    if not "%%a"=="" if not "%%a:~0,1%"=="#" (
        set "%%a=%%b"
    )
)

:: Activate virtual environment
echo [VENV] Activating virtual environment...
call venv\Scripts\activate.bat

:: Auto-detect XML file if not specified
if "%XML_FILE%"=="" (
    echo [AUTO-DETECT] XML_FILE is empty, searching for %MODULE% data file...

    :: Search for matching files in data directory
    set "FOUND_FILE="
    for %%f in ("data\*_%MODULE%.xml.gz") do (
        if exist "%%f" (
            set "FOUND_FILE=%%f"
            echo [AUTO-DETECT] Found file: %%f
        )
    )

    :: Search for matching files in current directory
    if "!FOUND_FILE!"=="" (
        for %%f in ("*_%MODULE%.xml.gz") do (
            if exist "%%f" (
                set "FOUND_FILE=%%f"
                echo [AUTO-DETECT] Found file: %%f
            )
        )
    )

    :: Set XML_FILE to found file or show error
    if not "!FOUND_FILE!"=="" (
        set "XML_FILE=!FOUND_FILE!"
        echo [AUTO-DETECT] Using auto-detected file: !XML_FILE!
    ) else (
        echo [ERROR] No %MODULE% data file found in auto-detect mode
        echo Expected file pattern: discogs_*_%MODULE%.xml.gz
        echo Search locations:
        echo   - data\*_%MODULE%.xml.gz
        echo   - *_%MODULE%.xml.gz
        echo.
        echo Please either:
        echo   1. Place the gzip file in data\ directory, or
        echo   2. Specify XML_FILE in %CONFIG_FILE%
        pause
        exit /b 1
    )
)

:: Check if specified/detected gzip file exists
if not exist "%XML_FILE%" (
    if not exist "data\%XML_FILE%" (
        echo [ERROR] Data file not found: %XML_FILE%
        echo Please ensure gzip file exists in current directory or data directory
        pause
        exit /b 1
    ) else (
        set "XML_FILE=data\%XML_FILE%"
    )
)

echo [DATA] Found data file: %XML_FILE%

:: Display current configuration
echo.
echo [CONFIG] Current configuration:
echo - Module: %MODULE_DISPLAY_NAME%
echo - Data file: %XML_FILE%
echo - MongoDB: %MONGO_URI%
echo - Database: %DB_NAME%
echo - Max records: %MAX_RECORDS%
echo - Output file: %OUTPUT_FILE%
echo - Progress file: %PROGRESS_FILE%
echo - Auto restart: %AUTO_RESTART%
echo - Max memory: %MAX_MEMORY_MB% MB
echo.

:: Ask for confirmation
set /p confirm="Confirm configuration is correct, start long-running processing? (y/N): "
if /i not "%confirm%"=="y" (
    echo [CANCEL] Operation cancelled
    pause
    exit /b 0
)

:: Copy necessary files to current directory
echo [SETUP] Preparing runtime environment...

:: Map module names to directory names
set SOURCE_DIR=%MODULE%
if "%MODULE%"=="labels" set SOURCE_DIR=label
if "%MODULE%"=="masters" set SOURCE_DIR=master

if not exist "enums.py" (
    if exist "..\..\!SOURCE_DIR!\enums.py" (
        copy "..\..\!SOURCE_DIR!\enums.py" "enums.py" >nul
        echo [SETUP] Copied enums.py from !SOURCE_DIR!
    ) else (
        copy "..\..\artists\enums.py" "enums.py" >nul
        echo [SETUP] Copied enums.py from artists (fallback)
    )
)

set PROCESS_SCRIPT=process_%MODULE%.py
set SOURCE_SCRIPT=%PROCESS_SCRIPT%

:: Map script names for modules with different naming conventions
if "%MODULE%"=="masters" set SOURCE_SCRIPT=process_master.py

if not exist "%PROCESS_SCRIPT%" (
    copy "..\..\!SOURCE_DIR!\!SOURCE_SCRIPT!" "%PROCESS_SCRIPT%" >nul
    if exist "%PROCESS_SCRIPT%" (
        echo [SETUP] Copied !SOURCE_SCRIPT! from !SOURCE_DIR! as %PROCESS_SCRIPT%
    ) else (
        echo [ERROR] Failed to copy !SOURCE_SCRIPT! from !SOURCE_DIR!
        pause
        exit /b 1
    )
)

:: Start long-running loop
echo.
echo ========================================
echo [START] Starting long-running processing...
echo Press Ctrl+C to safely stop processing
echo ========================================

set RUN_COUNT=0
set START_TIME=%TIME%

:MAIN_LOOP
set /a RUN_COUNT+=1
echo.
echo [%DATE% %TIME%] Starting run #%RUN_COUNT%...

:: Run processing script
venv\Scripts\python.exe %PROCESS_SCRIPT%
set LAST_EXIT_CODE=%ERRORLEVEL%

echo [%DATE% %TIME%] Run #%RUN_COUNT% completed, exit code: %LAST_EXIT_CODE%

:: Check if auto restart is needed
if /i "%AUTO_RESTART%"=="true" (
    if %LAST_EXIT_CODE% neq 0 (
        echo [RESTART] Error detected, waiting 30 seconds before restart...
        timeout /t 30 /nobreak >nul
        goto MAIN_LOOP
    ) else (
        echo [COMPLETE] Processing completed, program exiting normally
    )
) else (
    echo [EXIT] Auto restart disabled, program exiting
)

echo.
echo ========================================
echo [STATS] Processing End Statistics
echo ========================================
echo - Total runs: %RUN_COUNT%
echo - Start time: %START_TIME%
echo - End time: %TIME%
echo - Final exit code: %LAST_EXIT_CODE%
echo.

if exist "%OUTPUT_FILE%" (
    echo [LOG] Detailed log saved to: %OUTPUT_FILE%
)

pause
