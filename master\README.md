# Master 数据处理模块

## 概述

本模块用于处理 Discogs XML 数据中的 master 记录，将其解析并存储到 MongoDB 数据库中。

## 文件结构

```
master/
├── discogs_20250501_masters.xml  # Discogs master 数据 XML 文件
├── process_master.py              # 主处理脚本
├── enums.py                      # 枚举定义
├── process_output.txt            # 处理过程输出日志（运行时生成）
└── README.md                     # 本文档
```

## 功能特性

- **XML 解析**: 解析 Discogs master XML 文件
- **字段提取**: 提取核心字段和复杂嵌套结构
- **数据库存储**: 存储到 MongoDB 数据库
- **进度跟踪**: 实时显示处理进度
- **错误处理**: 完善的异常处理机制
- **统计报告**: 详细的处理结果统计

## 数据字段说明

### 核心字段

- `id`: 原始 master ID
- `y_id`: 系统生成的唯一标识符 (格式: YM1, YM2, YM3...)
- `key_release`: 主要发行版本 ID（原 XML 中的 main_release 字段）
- `title`: master 标题
- `year`: 发行年份

### 关联数据

- `artists`: 艺术家信息数组

  ```json
  [
    {
      "id": "艺术家ID",
      "name": "艺术家名称",
      "join": "连接符（可选）"
    }
  ]
  ```

- `genres`: 音乐类型数组

  ```json
  ["Electronic", "Rock", ...]
  ```

- `styles`: 音乐风格数组

  ```json
  ["Techno", "Deep House", ...]
  ```

- `notes`: 备注信息（从 XML 提取）

  ```json
  "Experimental Drum & Bass"
  ```

- `videos`: 视频信息数组（从 XML 提取）

  ```json
  [
    {
      "src": "https://www.youtube.com/watch?v=HxIAtSgCfJ4",
      "duration": "451",
      "embed": "true",
      "title": "Bubbles",
      "description": "Provided to YouTube by EPM Online..."
    }
  ]
  ```

- `images`: 图片信息数组（从数据库获取）
  ```json
  [
    {
      "type": "primary",
      "uri": "R-123456-1234567890.jpeg",
      "resource_url": "https://img.discogs.com/...",
      "uri150": "R-123456-1234567890-150.jpeg",
      "width": 600,
      "height": 600
    }
  ]
  ```

### 系统字段

- `delete_status`: 逻辑删除状态 (0: 未删除, 1: 已删除)
- `deleted_at`: 删除时间
- `created_at`: 创建时间
- `updated_at`: 更新时间
- `source`: 数据来源 (1: DISCOGS)
- `permissions`: 权限设置 (1: 全部可见)
- `status`: 状态 (1: 活跃)

## 配置参数

在 `process_master.py` 中可以配置以下参数：

```python
XML_FILE = 'discogs_20250501_masters.xml'  # XML 文件名
MONGO_URI = '**********************************************************'  # MongoDB 连接字符串
DB_NAME = 'music_test'  # 数据库名称
MAX_RECORDS = 1000  # 最大处理记录数
OUTPUT_FILE = 'process_output.txt'  # 输出日志文件
```

## 使用方法

### 1. 环境准备

确保已安装必要的依赖：

```bash
pip install pymongo
```

### 2. 运行处理脚本

```bash
cd discogs/master
python3 process_master.py
```

### 3. 查看结果

- 控制台会显示处理进度和最终统计
- 详细日志保存在 `process_output.txt` 文件中
- 数据存储在 MongoDB 的 `masters_new` 集合中

## 数据库集合

- `masters`: 原有的 master 数据集合
- `masters_new`: 新处理的 master 数据集合

处理过程中会：

1. 清空 `masters_new` 集合
2. 如果 `masters` 集合中存在相同 ID 的记录，会保留其 `_id`
3. 将处理后的数据插入或更新到 `masters_new` 集合

## 处理逻辑

1. **文件读取**: 逐行读取 XML 文件，识别 `<master>` 标签
2. **字段解析**: 使用正则表达式提取各个字段
3. **数据库查询**: 从现有 master 表中获取 images 等字段
4. **数据清理**: 去除空白字符，验证数据有效性
5. **数据库操作**: 检查现有记录，执行插入或更新操作
6. **进度跟踪**: 每处理 10 条记录显示一次进度
7. **统计输出**: 完成后输出详细的处理统计信息

## 注意事项

- 确保 MongoDB 服务正在运行且连接配置正确
- XML 文件应放在与脚本相同的目录下
- 处理大文件时可能需要较长时间，请耐心等待
- 可以通过修改 `MAX_RECORDS` 参数来限制处理的记录数量进行测试

## 错误处理

脚本包含完善的错误处理机制：

- 文件读取错误
- 数据库连接错误
- XML 解析错误
- 数据插入错误

所有错误信息都会记录到日志文件中。

## 性能优化

- 使用流式读取处理大文件
- 批量数据库操作减少连接开销
- 内存友好的逐行处理方式
- 可配置的处理记录数限制
